﻿using Lrb.Application.Agency.Web.Dtos;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Marketing.Web.Mapping;
using Lrb.Application.Marketing.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task MarketingChannelPartnerHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            BulkMarketingAgencyUploadTracker? bulkCpUpload = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingCpByTrackerIdSpec(input.TrackerId))).FirstOrDefault();
            try
            {
                if (bulkCpUpload != null)
                {
                    try
                    {
                        bulkCpUpload.MappedColumnData = bulkCpUpload.MappedColumnData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        bulkCpUpload.Status = Lrb.Domain.Enums.UploadStatus.Started;
                        bulkCpUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCpUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCpUpload);
                        Console.WriteLine($"handler() -> MarketingChannelPartnerHandler Updated Status: {bulkCpUpload.Status} \n {JsonConvert.SerializeObject(bulkCpUpload)}");

                        #region fetch all required data
                        var existingCps = await _cpRepository.ListAsync(new GetAllBulkACpsSpecs(), cancellationToken);
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        #endregion

                        #region Convert To DataTable
                        Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", bulkCpUpload.S3BucketKey);
                        DataTable dataTable = new();
                        if (bulkCpUpload.S3BucketKey.Split('.').LastOrDefault() == "csv")
                        {
                            using MemoryStream memoryStream = new();
                            fileStream.CopyTo(memoryStream);
                            dataTable = CSVHelper.CSVToDataTable(memoryStream);
                        }
                        else
                        {
                            dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, bulkCpUpload.SheetName);
                        }

                        List<InvalidChannelPartnerDto> invalids = new();
                        int totalRows = dataTable.Rows.Count;
                        for (int i = totalRows - 1; i >= 0; i--)
                        {
                            var row = dataTable.Rows[i];
                            var data1 = row[bulkCpUpload.MappedColumnData[MarketingDataColumns.Name]].ToString();
                            if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                            {
                                row.Delete();
                            }
                            else if (string.IsNullOrEmpty(row[bulkCpUpload.MappedColumnData[MarketingDataColumns.Name]].ToString()))
                            {
                                var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i.ToString())));
                                var invalidAgency = new InvalidChannelPartnerDto
                                {
                                    Errors = "Name is empty",
                                    //Notes = notes
                                };

                                row.Delete();
                            }
                        }
                        if (dataTable.Rows.Count <= 0)
                        {
                            throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                        }
                        totalRows = dataTable.Rows.Count;
                        Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        #endregion

                        #region Checking For new Prosperties and Project and Agencies

                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();

                        #endregion

                        var unMappedColumn = dataTable.GetUnmappedCpColumnNames(bulkCpUpload?.MappedColumnData);
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                        var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettingInfoList?.CountriesInfo);


                        var toChannelPartners = dataTable.ConvertToMarketingCp(bulkCpUpload.MappedColumnData, unMappedColumn, globalSettingInfoList, bulkCpUpload);


                        string callingCode = countries?.FirstOrDefault().DefaultCallingCode;

                        List<Task<(string ContactNo, string? AlternateContactNo)>> validationTasks = new List<Task<(string, string?)>>();

                        foreach (DataRow row in dataTable.Rows)
                        {
                            if (bulkCpUpload.MappedColumnData != null && bulkCpUpload.MappedColumnData.ContainsKey(MarketingDataColumns.Name) && bulkCpUpload.MappedColumnData.ContainsKey(MarketingDataColumns.PhoneNumber))
                            {
                                string countrycodeString = null;
                                if ((bulkCpUpload.MappedColumnData?.ContainsKey(MarketingDataColumns.CountryCode) ?? false) && (bulkCpUpload.MappedColumnData[MarketingDataColumns.CountryCode] != null))
                                {
                                    countrycodeString = row[bulkCpUpload.MappedColumnData[MarketingDataColumns.CountryCode]]?.ToString() ?? string.Empty;
                                }
                                if (string.IsNullOrWhiteSpace(countrycodeString))
                                {
                                    countrycodeString = callingCode ?? "+91";
                                }
                                int countrycode = 0;
                                try
                                {
                                    countrycode = Convert.ToInt32(countrycodeString);
                                }
                                catch
                                {
                                    countrycode = Convert.ToInt32(callingCode);

                                }

                                var globalSettingInfo = globalSettingInfoList;
                                string ContactNumber = row[bulkCpUpload.MappedColumnData[MarketingDataColumns.PhoneNumber]]?.ToString() ?? string.Empty;
                                string AlternativeContactNo = string.Empty;
                                int altercountrycode = 0;
                                string altrenativeCountryCodeString = null;
                                string name = null;
                                if ((bulkCpUpload.MappedColumnData[MarketingDataColumns.Name] != null))
                                {
                                    name = row[bulkCpUpload.MappedColumnData[MarketingDataColumns.Name]]?.ToString() ?? string.Empty;
                                }
                                Guid currentuserId = input.CurrentUserId;
                                var validationTask = ValidateProspectContactNoAsync1(ContactNumber, AlternativeContactNo, countrycode, altercountrycode, globalSettingInfo, toChannelPartners, invalids, name, currentuserId, cancellationToken);
                                validationTasks.Add(validationTask);
                            }
                            else if (bulkCpUpload.MappedColumnData != null && bulkCpUpload.MappedColumnData.ContainsKey(MarketingDataColumns.Name))
                            {
                                string name = null;
                                if ((bulkCpUpload.MappedColumnData[MarketingDataColumns.Name] != null))
                                {
                                    name = row[bulkCpUpload.MappedColumnData[MarketingDataColumns.Name]]?.ToString() ?? string.Empty;
                                }
                                foreach (var cp in toChannelPartners)
                                {
                                    if (cp.FirmName == name)
                                    {
                                        if (string.IsNullOrWhiteSpace(cp.FirmName))
                                        {

                                            var invalidCp = cp.Adapt<InvalidChannelPartnerDto>();
                                            invalidCp.Errors = "Invalid Name";
                                            invalids.Add(invalidCp);
                                        }

                                    }
                                }
                            }
                            else
                            {
                                throw new Exception("Name Need Map.");
                            }
                        }


                        List<string> contactNumbers = validationTasks.Where(t => !t.IsFaulted && !t.IsCanceled).Select(t => t.Result.ContactNo)
                        .Where(contactNo => !string.IsNullOrWhiteSpace(contactNo) && contactNo.Length <= 25)
                        .ToList();
                        List<string> altcontactNumbers = validationTasks.Where(t => !t.IsFaulted && !t.IsCanceled).Select(t => t.Result.AlternateContactNo)
                        .Where(i => !string.IsNullOrWhiteSpace(i) && i.Length <= 25)
                        .ToList();
                        toChannelPartners = toChannelPartners
                           .Where(cp => /*!string.IsNullOrEmpty(lead.ContactNo) && */cp.FirmName != string.Empty)
                           .DistinctBy(cp => cp.FirmName)
                           .ToList();
                        validationTasks = validationTasks
                        .Where(t => !t.IsFaulted && !t.IsCanceled && !string.IsNullOrWhiteSpace(t.Result.ContactNo) && t.Result.ContactNo.Length <= 25)
                        .DistinctBy(t => t.Result.ContactNo)
                        .ToList();
                        var distinctCount = toChannelPartners.Count();
                        Console.WriteLine($"handler() -> Total Distinct ChannelPartner: {distinctCount}");
                        var existingContactNos = existingCps.Select(i => i.FirmName).ToList();
                        List<ChannelPartner> cpToUpdate = new();
                        foreach (ChannelPartner cp in toChannelPartners)
                        {


                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(cp.ContactNo)))
                            {
                                var invalidCp = cp.Adapt<InvalidChannelPartnerDto>();

                                invalidCp.Errors = "Duplicate ChannelPartner";
                                var duplicateCp = existingCps.FirstOrDefault(i => !string.IsNullOrWhiteSpace(cp.FirmName) && i.FirmName != null && i.FirmName.Equals(cp.FirmName));
                                if (duplicateCp != null)
                                {
                                    invalids.Add(invalidCp);
                                }
                            }
                        }
                        toChannelPartners.RemoveAll(i => cpToUpdate.Select(i => i.ContactNo).Contains(i.ContactNo));
                        toChannelPartners.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));

                        bulkCpUpload.Status = UploadStatus.InProgress;
                        bulkCpUpload.TotalCount = totalRows;
                        bulkCpUpload.DistinctCount = distinctCount;
                        bulkCpUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCpUpload.CreatedBy = input.CurrentUserId;
                        if (invalids.Any())
                        {
                            bulkCpUpload.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate ChannelPartner").Count();
                            bulkCpUpload.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();
                            bulkCpUpload.Count = cpToUpdate.Count();
                            byte[] bytes = MarketingChannelParterHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidChannelPartner-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = "ChannelPartner";
                            var key = await _blobStorageService?.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            bulkCpUpload.InvalidS3BucketKey = key;
                        }
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCpUpload);
                        // Console.WriteLine($"handler() -> BulkProspectUploadTracker Updated Status: {bulkProspectUpload.Status} \n {JsonConvert.SerializeObject(bulkProspectUpload)}");
                        BulkChannelPartnerUploadBackgroundDto backgroundDto = new();
                        if (toChannelPartners.Count > 0)
                        {
                            int agencyPerchunk = toChannelPartners.Count > 5000 ? 5000 : toChannelPartners.Count;
                            var chunks = toChannelPartners.Chunk(agencyPerchunk).Select(i => new ConcurrentBag<ChannelPartner>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkChannelPartnerUploadBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = bulkCpUpload.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    ChannelPartners = new(chunk),
                                    UserIds = new(bulkCpUpload.UserIds ?? new()),
                                    Users = users.ToList()
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }
                        bulkCpUpload.Status = UploadStatus.Completed;
                        bulkCpUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCpUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCpUpload);
                    }

                    catch (Exception ex)
                    {
                        bulkCpUpload = await _bulkMarketingUploadRepo.GetByIdAsync(bulkCpUpload.Id);
                        bulkCpUpload.Status = UploadStatus.Failed;
                        bulkCpUpload.Message = ex.Message;
                        bulkCpUpload.LastModifiedBy = input.CurrentUserId;
                        bulkCpUpload.CreatedBy = input.CurrentUserId;
                        await _bulkMarketingUploadRepo.UpdateAsync(bulkCpUpload);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingChannelPartnerHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkProspectleadUploadTrackerUsingEPPlus -> MarketingChannelPartnerHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkChannelPartnerUploadBackgroundDto dto)
        {
            BulkChannelPartnerUploadTracker bulkChannelPartner = new();
            var tracker = (await _bulkMarketingUploadRepo.ListAsync(new GetBulkMarketingAgencyByTrackerIdSpec(dto.TrackerId))).FirstOrDefault();
            try
            {
                try
                {
                    await _cpRepository.AddRangeAsync(dto.ChannelPartners);
                    await _dapperRepository.UpdateLatModifiedDateAsync(dto?.TenantInfoDto?.Id, (int)EntityType.ChannelPartner);

                }
                catch (Exception e)
                {

                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.ChannelPartners.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _bulkMarketingUploadRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _bulkMarketingUploadRepo.UpdateAsync(tracker);
            }
        }
        public async Task<(string ContactNo, string? AltContactNo)> ValidateProspectContactNoAsync1(string contactNum, string? alternateContactNum, int countryCode, int alternativecountryCode, GlobalSettings globalSettingInfo, List<ChannelPartner> channelPartners, List<InvalidChannelPartnerDto> invalids, string? name, Guid currentUserId, CancellationToken cancellationToken = default)
        {
            string contactNo = Regex.Replace(contactNum, "[^0-9]", "");
            string alternateContactNo = Regex.Replace(alternateContactNum, "[^0-9]", "");
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettingInfo?.CountriesInfo);

            try
            {

                string altContactWithCountryCode = null;

                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countryCode, new List<string>());
                string defaultRegion = regionCodes.FirstOrDefault();
                if (defaultRegion == null)
                {
                    defaultRegion = countries?.FirstOrDefault()?.Code ?? "IN";
                }
                PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");

                string altNumericMobileNumber = string.Empty;
                PhoneNumber phoneNumberAltcontNo = null;
                string altContactWithCountryCode1 = string.Empty;
                if (alternateContactNo != string.Empty && alternativecountryCode != 0)
                {
                    List<string> regionCodesforaltcontactNo = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(alternativecountryCode, new List<string>());
                    string defaultRegionforAltContNo = regionCodesforaltcontactNo.FirstOrDefault();
                    if (defaultRegionforAltContNo == null)
                    {
                        defaultRegionforAltContNo = countries?.FirstOrDefault()?.Code ?? "IN";
                    }
                    try
                    {
                        phoneNumberAltcontNo = phoneUtil.Parse(alternateContactNo, defaultRegionforAltContNo);

                        PhoneNumber numberExamplealtConctNum = phoneUtil.GetExampleNumberForType(defaultRegionforAltContNo, PhoneNumberType.MOBILE);
                        string formattedNumberaltContNo = phoneUtil.Format(numberExamplealtConctNum, PhoneNumberFormat.E164);
                        altContactWithCountryCode = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                        altNumericMobileNumber = Regex.Replace(formattedNumberaltContNo, @"\D", "");
                    }
                    catch
                    {
                    }
                }
                bool isValid;
                if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
                {
                    isValid = true;
                    try
                    {
                        foreach (var cp in channelPartners)
                        {
                            if (Regex.Replace(cp.ContactNo, "[^0-9]", "") == contactNo && cp.FirmName == name)
                            {
                                cp.ContactNo = contactWithCountryCode;
                                cp.CreatedBy = currentUserId;
                                cp.LastModifiedBy = currentUserId;
                                if (string.IsNullOrWhiteSpace(cp.FirmName))
                                {
                                    var invalidCp = cp.Adapt<InvalidChannelPartnerDto>();
                                    invalidCp.Errors = "Invalid Name";
                                    invalids.Add(invalidCp);
                                }

                            }

                        }
                    }
                    catch
                    {

                    }
                }
                else if ((contactNo.Length > 6 && contactNo.Length <= 20))
                {
                    isValid = true;

                    try
                    {
                        foreach (var cp in channelPartners)
                        {
                            if (Regex.Replace(cp.ContactNo, "[^0-9]", "") == contactNo && cp.FirmName == name)
                            {
                                if (contactNum.StartsWith("+"))
                                {
                                    cp.ContactNo = "+" + contactNo;
                                    cp.CreatedBy = currentUserId;
                                    cp.LastModifiedBy = currentUserId;
                                }
                                else
                                {
                                    cp.ContactNo = "+" + countryCode + contactNo;
                                    cp.CreatedBy = currentUserId;
                                    cp.LastModifiedBy = currentUserId;

                                }
                                if (string.IsNullOrWhiteSpace(cp.FirmName))
                                {

                                    var invalidCp = cp.Adapt<InvalidChannelPartnerDto>();
                                    invalidCp.Errors = "Invalid Name";
                                    invalids.Add(invalidCp);
                                }

                            }
                        }
                    }
                    catch
                    {

                    }
                }
                else
                {
                    isValid = false;
                }

                if (!isValid)
                {
                    return (string.Empty, string.Empty);
                }
                else
                {
                    if (phoneUtil.GetRegionCodeForNumber(phoneNumber) != defaultRegion)
                    {
                        throw new Exception("Invalid ContactNo - International numbers not allowed");
                    }
                    string contactWithCountryCode1 = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                    if (phoneNumberAltcontNo != null)
                    {
                        altContactWithCountryCode1 = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                    }
                    return (contactWithCountryCode1, altContactWithCountryCode1);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }


}
