﻿using Amazon.DynamoDBv2.Model;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.Integration.Mobile.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Data;
using System.Text.RegularExpressions;

namespace ExcelUpload
{
    public class MigrateBulkLeadleadUploadTrackerUsingEPPlus
    {
        public string? S3BucketKey { get; set; }
        public Dictionary<DataColumns, string>? MappedColumnsData { get; set; }
        public List<string>? UserIds { get; set; }
        public Guid TrackerId { get; set; }
    }
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task MigrateLeadHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            //GEt the Tracker by Id 
            LeadMigrateTracker? leadMigrateTracker = await _leadMigrateTrackerRepo.GetByIdAsync(input.TrackerId);
            Console.WriteLine($"handler() -> BulkLeadUploadTracker GetById(): {JsonConvert.SerializeObject(leadMigrateTracker)}");
            try
            {
                if (leadMigrateTracker != null)
                {
                    try
                    {
                        leadMigrateTracker.MappedColumnsData = leadMigrateTracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                        leadMigrateTracker.Status = UploadStatus.Started;
                        leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        leadMigrateTracker.CreatedBy = input.CurrentUserId;
                        var migrationType = leadMigrateTracker.MigrationType;
                        var currentUser = input.CurrentUserId;
                        //var migrationType = LeadMigrationType.UpdateExistingLead;
                        await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadMigrateTracker.Status} \n {JsonConvert.SerializeObject(leadMigrateTracker)}");
                        #region Fetch all required MasterData and Other data
                        List<Project> tempProjects = new(await _newProjectRepo.ListAsync(cancellationToken));
                        List<Property> properties = new(await _propertyRepo.ListAsync(cancellationToken));
                        List<MasterPropertyType> propetyTypes = new(await _propertyTypeRepo.ListAsync(cancellationToken));
                        List<Lrb.Application.Identity.Users.UserDetailsDto> users = new(await _userService.GetListAsync(cancellationToken));
                        DuplicateLeadFeatureInfo? duplicateFeatureInfo = (await _duplicateFeatureInfo.ListAsync()).FirstOrDefault();
                        List<CustomMasterLeadStatus> leadStatuses = new(await _customMastereadStatus.ListAsync(CancellationToken.None));
                        List<Lead> existingLeads = new();
                        List<CustomFlag> flags = new(await _customflags.ListAsync(cancellationToken));
                        List<Flag> flag = new(await _flagRepo.ListAsync(cancellationToken));
                        var subSources = (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(input.TenantId)).ToList();

                        List<Agency> agencies = new((await _agencyRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                        List<ChannelPartner> channelPartner = new((await _cpRepository.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.FirmName)));
                        List<Campaign> campaigns = new((await _campaignRepo.ListAsync(cancellationToken)).Where(i => !string.IsNullOrWhiteSpace(i.Name)));
                        #endregion

                        #region Convert file to Datatable
                        DataTable dataTable = new();
                        List<InvalidData> invalids = new();
                        int totalRows = 0;
                        UploadType uploadType = UploadType.None;
                        if (!string.IsNullOrWhiteSpace(leadMigrateTracker.S3BucketKey))
                        {
                            Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", leadMigrateTracker.S3BucketKey);
                            if (leadMigrateTracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                            {
                                using MemoryStream memoryStream = new();
                                fileStream.CopyTo(memoryStream);
                                dataTable = CSVHelper.CSVToDataTable(memoryStream);
                                uploadType = UploadType.CSV;
                            }
                            else
                            {
                                dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, leadMigrateTracker.SheetName);
                                uploadType = UploadType.Excel;
                            }
                            totalRows = dataTable.Rows.Count;
                            for (int i = totalRows - 1; i >= 0; i--)
                            {
                                DataRow row = dataTable.Rows[i];
                                if (row.ItemArray.All(i => string.IsNullOrEmpty(i?.ToString())))
                                {
                                    row.Delete();
                                }
                                else if (string.IsNullOrEmpty(row[leadMigrateTracker.MappedColumnsData?[DataColumns.Name] ?? string.Empty].ToString()) && string.IsNullOrEmpty(row[leadMigrateTracker.MappedColumnsData?[DataColumns.ContactNo] ?? string.Empty].ToString()))
                                {
                                    var notes = string.Join(",", row.ItemArray.Where(i => !string.IsNullOrEmpty(i?.ToString())));
                                    var invalidData = new InvalidData
                                    {
                                        Errors = "Contact number and name are empty.",
                                        Notes = notes
                                    };
                                    if (!invalids.Any(i => i.Notes == invalidData.Notes))
                                    {
                                        invalids.Add(invalidData);
                                    }
                                    row.Delete();
                                }
                            }
                            if (dataTable.Rows.Count <= 0)
                            {
                                throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                            }
                            totalRows = dataTable.Rows.Count;
                            Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");
                        }
                        #endregion
                        #region checking For new Properties or projects
                        List<Property> newProperties = new();
                        List<Lrb.Domain.Entities.Project> newProjects = new();
                        List<Agency> newAgencies = new();
                        List<ChannelPartner> newChannels = new();
                        List<Campaign> newCampaign = new();

                        if (((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.Property] != null))
                            || ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.Project] != null))
                            || ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.AgencyName] != null))
                            || ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null))
                            || ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.CampaignName] != null)))
                        {
                            var existingPropertynames = properties.Where(i => i != null && !string.IsNullOrEmpty(i.Title)).Select(i => i.Title?.ToLower().Trim()).ToList();
                            var existingprojctnames = tempProjects.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var existingAgencyNames = agencies.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();
                            var exstingChannelPartners = channelPartner.Where(i => i != null && !string.IsNullOrEmpty(i.FirmName)).Select(i => i.FirmName?.ToLower().Trim()).ToList();
                            var exstingCampaigns = campaigns.Where(i => i != null && !string.IsNullOrEmpty(i.Name)).Select(i => i.Name?.ToLower().Trim()).ToList();

                            var isPropertyPresent = ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.Property) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.Property] != null));
                            var isProjectPresent = ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.Project) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.Project] != null));
                            var isAgencyNamePresent = ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.AgencyName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.AgencyName] != null));
                            var isChannelPresent = ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.ChannelPartnerName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.ChannelPartnerName] != null));
                            var isCampaignPresent = ((leadMigrateTracker.MappedColumnsData?.ContainsKey(DataColumns.CampaignName) ?? false) && (leadMigrateTracker.MappedColumnsData[DataColumns.CampaignName] != null));

                            dataTable.AsEnumerable().ToList().ForEach(row =>
                            {
                                if (isPropertyPresent)
                                {
                                    var propertyName = row[leadMigrateTracker.MappedColumnsData[DataColumns.Property]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(propertyName) && !(existingPropertynames?.Contains(propertyName.ToLower().Trim()) ?? false) && !newProperties.Select(i => i.Title).Contains(propertyName))
                                    {
                                        newProperties.Add(new()
                                        {
                                            Title = propertyName.Trim(),
                                        });
                                    }
                                }
                                if (isProjectPresent)
                                {
                                    var projectName = row[leadMigrateTracker.MappedColumnsData[DataColumns.Project]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(projectName) && !(existingprojctnames?.Contains(projectName.ToLower()) ?? false) && !newProjects.Select(i => i.Name).Contains(projectName))
                                    {
                                        newProjects.Add(new()
                                        {
                                            Name = projectName.Trim(),
                                        });
                                    }
                                }
                                if (isAgencyNamePresent)
                                {
                                    var agencyName = row[leadMigrateTracker.MappedColumnsData[DataColumns.AgencyName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(agencyName) && !(existingAgencyNames?.Contains(agencyName.ToLower()) ?? false) && !newAgencies.Select(i => i.Name).Contains(agencyName))
                                    {
                                        newAgencies.Add(new()
                                        {
                                            Name = agencyName,
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isChannelPresent)
                                {
                                    var cpName = row[leadMigrateTracker.MappedColumnsData[DataColumns.ChannelPartnerName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(cpName) && !(exstingChannelPartners?.Contains(cpName.ToLower().Trim()) ?? false) && !newChannels.Select(i => i.FirmName).Contains(cpName))
                                    {
                                        newChannels.Add(new()
                                        {
                                            FirmName = cpName.Trim(),
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                                if (isCampaignPresent)
                                {
                                    var campaignName = row[leadMigrateTracker.MappedColumnsData[DataColumns.CampaignName]]?.ToString();
                                    if (!string.IsNullOrWhiteSpace(campaignName) && !(exstingCampaigns?.Contains(campaignName.ToLower()) ?? false) && !newCampaign.Select(i => i.Name).Contains(campaignName))
                                    {
                                        newCampaign.Add(new()
                                        {
                                            Name = campaignName,
                                            CreatedBy = leadMigrateTracker.CreatedBy,
                                            LastModifiedBy = leadMigrateTracker.LastModifiedBy,
                                        });
                                    }
                                }
                            });
                        }
                        if (newProperties.Any())
                        {
                            await _propertyRepo.AddRangeAsync(newProperties);
                            properties.AddRange(newProperties);
                        }
                        if (newProjects.Any())
                        {
                            await _newProjectRepo.AddRangeAsync(newProjects);
                            tempProjects.AddRange(newProjects);
                        }
                        if (newAgencies.Any())
                        {
                            await _agencyRepo.AddRangeAsync(newAgencies);
                            agencies.AddRange(newAgencies);
                        }
                        if (newChannels.Any())
                        {
                            await _cpRepository.AddRangeAsync(newChannels);
                            channelPartner.AddRange(newChannels);
                        }
                        if (newCampaign.Any())
                        {
                            await _campaignRepo.AddRangeAsync(newCampaign);
                            campaigns.AddRange(newCampaign);
                        }
                        #endregion

                        var unMappedColumns = dataTable.GetUnmappedColumnNames(leadMigrateTracker.MappedColumnsData ?? new());
                        var globalSettingInfoList = await _globalSettingsRepository.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                        var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettingInfoList?.CountriesInfo);

                        List<Lead> leads = await dataTable.MigrateAsync(leadMigrateTracker.MappedColumnsData, unMappedColumns, propetyTypes, leadStatuses, users,
                            _propertyRepo, _newProjectRepo, _agencyRepo, subSources, agencies, leadMigrateTracker, globalSettingInfoList, flags, flag, 
                            channelPartner, campaigns,currentUser,input.JsonData ?? string.Empty);
                        string callingCode = countries?.FirstOrDefault().DefaultCallingCode;
                        leads = leads.DistinctBy(i => i.ContactNo).ToList();
                        foreach (var lead in leads)
                        {

                            if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                            {
                                var contactNo = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ContactNo, globalSettingInfoList);
                                if (string.IsNullOrWhiteSpace(contactNo))
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidLead);
                                }
                                lead.ContactNo = contactNo;
                                if (!string.IsNullOrWhiteSpace(lead.AlternateContactNo)) 
                                {
                                    var altcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.AltCountryCode, lead.AlternateContactNo, globalSettingInfoList);
                                    lead.AlternateContactNo = altcontactno;
                                }
                                if (!string.IsNullOrWhiteSpace(lead.ReferralContactNo))
                                {
                                    var refferalcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ReferralContactNo, globalSettingInfoList);
                                    lead.ReferralContactNo = refferalcontactno;
                                }
                                if (!string.IsNullOrWhiteSpace(lead.ChannelPartnerContactNo))
                                {
                                    var refferalcontactno = BulkUploadHelper.ConcatenatePhoneNumber(lead.CountryCode, lead.ChannelPartnerContactNo, globalSettingInfoList);
                                    lead.ChannelPartnerContactNo = refferalcontactno;
                                }
                            }
                            else
                            {
                                var invalidLead = lead.Adapt<InvalidData>();
                                invalidLead.Errors = "Invalid ContactNo";
                                invalids.Add(invalidLead);
                            }
                        }

                        var distinctLeadCount = leads.Count();

                        if (!(migrationType == LeadMigrationType.CreateDuplicateLead && duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded))
                        {
                            var contactNos = leads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList(); ;
                            foreach (var contactNo in contactNos)
                            {
                                string mobileNumber = Regex.Replace(contactNo, "[^0-9]", "");
                                string defaultRegion = string.Empty;
                                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                                try
                                {
                                    if (contactNo.StartsWith("+") && contactNo.Length > 6 && contactNo.Length < 20)
                                    {
                                        PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                                        defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                                        string countryCode = phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                                        countryCode = Regex.Replace(countryCode, "[^0-9]", "");
                                        if (mobileNumber.StartsWith(countryCode))
                                        {
                                            mobileNumber = mobileNumber.Substring(countryCode.Length);
                                        }
                                    }
                                }
                                catch
                                {
                                }
                                var leadsForNumber = await _leadRepo.ListAsync(new CheckDuplicateLeadsSpecsV1(mobileNumber), cancellationToken);
                                if (leadsForNumber != null)
                                {
                                    existingLeads.AddRange(leadsForNumber);
                                }

                            }

                        }
                        Console.WriteLine($"handler() -> Total Distinct Lead: {distinctLeadCount}");
                        var existingContactNos = existingLeads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();;
                        List<Lead> leadsToUpdate = new();
                        List<Lead> existingLeadsToUpdate = new();
                        foreach (Lead lead in leads)
                        {

                            if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(lead.ContactNo)))
                            {
                                if (migrationType == LeadMigrationType.UpdateMissingInformation)
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Duplicate Lead";
                                    var duplicateLead = existingLeads.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(lead.ContactNo) && ((i.ContactNo?.Contains(lead.ContactNo) == true) || (i.AlternateContactNo?.Contains(lead.ContactNo) == true))) || (!string.IsNullOrWhiteSpace(lead.AlternateContactNo) && ((i.ContactNo?.Contains(lead.AlternateContactNo) == true) || (i.AlternateContactNo?.Contains(lead.AlternateContactNo) == true))));
                                    if (duplicateLead != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateLead.AssignTo);
                                        invalidLead.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        leadsToUpdate.Add(lead);
                                    }
                                    foreach (var leadToUpdate in leadsToUpdate)
                                    {
                                        foreach (var property in leadToUpdate.GetType().GetProperties())
                                        {
                                            if (property.GetValue(leadToUpdate) == null)
                                            {
                                                var newValue = lead.GetType().GetProperty(property.Name)?.GetValue(lead);
                                                if (newValue != null)
                                                {
                                                    property.SetValue(leadToUpdate, newValue);
                                                }
                                            }
                                        }
                                    }
                                }
                                else if (migrationType == LeadMigrationType.OverideExisitingLeadInformation)
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Duplicate Lead";
                                    var duplicateLead = existingLeads.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(lead.ContactNo) && ((i.ContactNo?.Contains(lead.ContactNo) == true) || (i.AlternateContactNo?.Contains(lead.ContactNo) == true))) || (!string.IsNullOrWhiteSpace(lead.AlternateContactNo) && ((i.ContactNo?.Contains(lead.AlternateContactNo) == true) || (i.AlternateContactNo?.Contains(lead.AlternateContactNo) == true))));
                                    if (duplicateLead != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateLead.AssignTo);
                                        invalidLead.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        leadsToUpdate.Add(lead);
                                    }
                                   
                                }
                                else
                                {
                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Duplicate Lead";
                                    var duplicateLead = existingLeads.FirstOrDefault(i => (!string.IsNullOrWhiteSpace(lead.ContactNo) && ((i.ContactNo?.Contains(lead.ContactNo) == true) || (i.AlternateContactNo?.Contains(lead.ContactNo) == true))) || (!string.IsNullOrWhiteSpace(lead.AlternateContactNo) && ((i.ContactNo?.Contains(lead.AlternateContactNo) == true) || (i.AlternateContactNo?.Contains(lead.AlternateContactNo) == true))));
                                    if (duplicateLead != null)
                                    {
                                        var user = users.FirstOrDefault(i => i.Id == duplicateLead.AssignTo);
                                        invalidLead.AssignTo = user != null ? user.FirstName + " " + user.LastName : string.Empty;
                                        leadsToUpdate.Add(lead);

                                     
                                        invalidLead.Source = duplicateLead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? duplicateLead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                        invalidLead.SubSource = duplicateLead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? duplicateLead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                                        invalidLead.Created = duplicateLead.CreatedOn.Date;
                                        invalids.Add(invalidLead);
                                    }
                                }
                            }
                        }
                        leads.RemoveAll(i => invalids.Select(i => i.ContactNo).Contains(i.ContactNo));
                        leads.RemoveAll(i => existingContactNos.Select(i => i).Contains(i.ContactNo));
                        Parallel.ForEach(leads, lead =>
                        {
                            lead.UploadType = uploadType;
                            lead.UploadTypeName = leadMigrateTracker.S3BucketKey;
                        });
                        //update Tracker
                        leadMigrateTracker.Status = UploadStatus.InProgress;
                        leadMigrateTracker.TotalCount = totalRows;
                        leadMigrateTracker.DistinctLeadCount = distinctLeadCount;
                        leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        leadMigrateTracker.CreatedBy = input.CurrentUserId;


                        if (invalids.Any())
                        {
                            leadMigrateTracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Lead").Count();
                            leadMigrateTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Parent Lead Not Found" || i.Errors == "Child Lead Not Found").Count();
                            byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                            string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                            string folder = $"{input.TenantId}/Leads/Migrate";
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                            //var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            leadMigrateTracker.InvalidDataS3BucketKey = key;
                        }
                        if (existingLeadsToUpdate.Any())
                        {
                            leadMigrateTracker.LeadsUpdatedCount = existingLeadsToUpdate.Count();
                        }
                        await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        Console.WriteLine($"handler() -> BulkLeadUploadTracker Updated Status: {leadMigrateTracker.Status} \n {JsonConvert.SerializeObject(leadMigrateTracker)}");
                        BulkMigrateBackgroundDto backgroundDto = new();
                        if (leads.Count > 0)
                        {
                            int leadsPerchunk = leads.Count > 5000 ? 5000 : leads.Count;
                            var chunks = leads.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));
                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;
                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                await ExecuteDBOperationsAsync(backgroundDto);
                                chunkIndex++;
                            }
                        }

                        if ((leadsToUpdate?.Any() ?? false) && (migrationType == LeadMigrationType.UpdateMissingInformation || migrationType == LeadMigrationType.OverideExisitingLeadInformation))
                        {
                            Parallel.ForEach(leadsToUpdate, lead =>
                            {
                                lead.UploadType = uploadType;
                                lead.UploadTypeName = leadMigrateTracker.S3BucketKey;
                            });

                            int leadsPerchunk = leadsToUpdate.Count > 5000 ? 5000 : leadsToUpdate.Count;
                            var chunks = leadsToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));

                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;

                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                if (backgroundDto.Leads.Any())
                                {
                                    await UpdateDuplicateLeadsAsync(backgroundDto, tempProjects, properties, invalids, input);
                                    chunkIndex++;
                                }


                            }
                        }
                        if (existingLeadsToUpdate?.Any() ?? false && (migrationType == LeadMigrationType.UpdateMissingInformation || migrationType == LeadMigrationType.OverideExisitingLeadInformation))
                        {
                            Parallel.ForEach(existingLeadsToUpdate, lead =>
                            {
                                lead.UploadType = uploadType;
                                lead.UploadTypeName = leadMigrateTracker.S3BucketKey;
                            });

                            int leadsPerchunk = existingLeadsToUpdate.Count > 5000 ? 5000 : existingLeadsToUpdate.Count;
                            var chunks = existingLeadsToUpdate.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Lead>(i));

                            List<Task> tasks = new();
                            var currentUserId = _currentUser.GetUserId();
                            var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);

                            if (currentUserId == Guid.Empty)
                            {
                                currentUserId = input.CurrentUserId;
                            }
                            Console.WriteLine($"handler() -> CurrentUserId: {currentUserId}");
                            var chunkIndex = 1;

                            foreach (var chunk in chunks.ToList())
                            {
                                backgroundDto = new BulkMigrateBackgroundDto()
                                {
                                    CurrentUserId = currentUserId,
                                    TrackerId = leadMigrateTracker.Id,
                                    TenantInfoDto = tenantInfo,
                                    CancellationToken = CancellationToken.None,
                                    Leads = new(chunk),
                                    Users = users.ToList(),
                                    MigrationType = migrationType
                                };
                                if (backgroundDto.Leads.Any())
                                {
                                    await UpdateDuplicateLeadsAsync(backgroundDto, tempProjects, properties, invalids, input);
                                    chunkIndex++;
                                }


                            }
                        }
                        leadMigrateTracker.Status = UploadStatus.Completed;
                        leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                        leadMigrateTracker.CreatedBy = input.CurrentUserId;
                        leadMigrateTracker.TotalUploadedCount = leads.Count;
                        await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        if (leads.Any() || (leadsToUpdate?.Any() ?? false) || (existingLeadsToUpdate?.Any() ?? false))
                        {
                            var tracker = await _leadMigrateTrackerRepo.GetByIdAsync(backgroundDto.TrackerId);
                            try
                            {
                                await SendNotificationsAsync(backgroundDto);
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception(Notification): {JsonConvert.SerializeObject(e)}");
                                if (tracker != null)
                                {
                                    if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                                    {
                                        tracker.Status = UploadStatus.Completed;
                                    }
                                    else
                                    {
                                        tracker.Status = UploadStatus.Failed;
                                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                                    }
                                    tracker.LastModifiedBy = backgroundDto.CurrentUserId;
                                    tracker.CreatedBy = backgroundDto.CurrentUserId;
                                    await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                        ErrorSource = e?.Source,
                                        StackTrace = e?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                        ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync() -> SendNotificationsAsync()",
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        leadMigrateTracker = await _leadMigrateTrackerRepo.GetByIdAsync(leadMigrateTracker.Id);
                        if (leadMigrateTracker != null)
                        {
                            leadMigrateTracker.Status = UploadStatus.Failed;
                            leadMigrateTracker.Message = ex.Message;
                            leadMigrateTracker.LastModifiedBy = input.CurrentUserId;
                            leadMigrateTracker.CreatedBy = input.CurrentUserId;
                            await _leadMigrateTrackerRepo.UpdateAsync(leadMigrateTracker);
                        }
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> LeadHandler()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
                else
                {
                    Console.WriteLine($"handler() -> tracker not found by the Id : {input.TrackerId}");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> LeadHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }

            //return new(leadUploadTracker);
        }
        private async Task SendNotificationsAsync(BulkMigrateBackgroundDto dto)
        {

            var userIdsWithNoOfLeadsAssigned = dto.Leads.Where(i => i.AssignTo != Guid.Empty).GroupBy(i => i.AssignTo).ToDictionary(i => i.Key, j => j.Count());
            if (userIdsWithNoOfLeadsAssigned.Count() > 0)
            {
                foreach (var item in userIdsWithNoOfLeadsAssigned)
                {
                    try
                    {
                        var userDetails = dto.Users?.FirstOrDefault(i => i.Id == item.Key);
                        if (userDetails != null)
                        {
                            if (userDetails.Id != dto.CurrentUserId)
                            {
                                var lead = dto.Leads.FirstOrDefault(i => i.AssignTo == item.Key);
                                if (item.Value == 1)
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.LeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                                else
                                {
                                    Console.WriteLine($"BulkLeadUpload -> FunctionEntryPoint -> SendNotificationsAsync() -> Event: {Lrb.Domain.Enums.Event.MultipleLeadAssignment}, lead: {JsonConvert.SerializeObject(lead, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, UserDetails: {JsonConvert.SerializeObject(userDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}, NoOfLeads: {item.Value}");
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.MultipleLeadAssignment, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, null, item.Value, dto.CurrentUserId);
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> SendNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            var unAssignedLeads = dto?.Leads.Where(i => i.AssignTo == Guid.Empty)?.ToList() ?? new();
            if (unAssignedLeads.Any())
            {
                List<Guid>? adminIds = await _npgsqlRepo.GetAdminIdsAsync(dto?.TenantInfoDto?.Id ?? string.Empty);
                var leadForNotification = unAssignedLeads.FirstOrDefault();
                if (adminIds != null && adminIds.Any())
                {
                    var response = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.UnAssignedLeadUpdate, leadForNotification, Guid.Empty, null, topics: new List<string> { leadForNotification?.CreatedBy.ToString() ?? string.Empty, leadForNotification?.LastModifiedBy.ToString() ?? string.Empty }, unAssignedLeads.Count, dto?.CurrentUserId, adminIds);
                }
            }
        }
        private async Task ExecuteDBOperationsAsync(BulkMigrateBackgroundDto dto)
        {
            if (!(dto.Leads?.Any() ?? false))
            {
                return;
            }
            LeadMigrateTracker? tracker = await _leadMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);
            try
            {
                if (dto.MigrationType == LeadMigrationType.CreateDuplicateLead)
                {
                    foreach (var lead in dto.Leads)
                    {
                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo));
                        if (parentLead != null)
                        {
                            lead.RootId = parentLead.Id;
                            lead.DuplicateLeadVersion = "D" + (parentLead.ChildLeadsCount + 1);
                            lead.ParentLeadId = parentLead.Id;
                            parentLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(parentLead);
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),

                                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync() -> SendNotificationsAsync()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                        }
                    }
                }
                 DateTime[] createdOnStrings = new DateTime[dto.Leads.Count];
                for (int i = 0; i < dto.Leads.Count; i++)
                {
                    createdOnStrings[i] = dto.Leads[i].CreatedOn.ConvertAndSetKindAsUtc();// Convert to UTC
                }
                foreach (var lead in dto.Leads)
                {
                    var appoinment = lead.Appointments;
                    if (appoinment != null)
                    {
                        foreach (var api in appoinment)
                        {
                            api.UserId = lead.AssignTo;
                        }
                    }
                }
                var leads = await _leadRepo.AddRangeAsync(dto.Leads);

                List<Lead> leadsList = leads.ToList();
                for (int i = 0; i < leadsList.Count; i++)
                {
                    leadsList[i].CreatedOn = createdOnStrings[i];
                }
                var addresses = ExtractAddressFromLeads(leads.ToList());
                await AddBulkLocations(addresses);
                var leadDtos = dto.Leads.Adapt<List<ViewLeadDto>>();
                dto.LeadDtos = leadDtos;
                List<LeadHistory> leadHistories = new();
                leadDtos.ForEach(leadDto =>
                {
                    if (dto.Users != null && dto.Users.Count > 0)
                    {
                        leadDto.SetUsersInViewLeadDto(dto.Users, currentUserId: dto.CurrentUserId);
                    }
                    leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));
                });
                leadHistories = (await _leadHistoryRepo.AddRangeAsync(leadHistories)).ToList();
                List<LeadHistory> leadsHistory = leadHistories.ToList();
                for (int i = 0; i < leadsHistory.Count; i++)
                {
                    leadsHistory[i].CreatedDate = createdOnStrings[i];
                }
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.Leads.Count;
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                }
            }

            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker != null)
                {
                    if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    else
                    {
                        tracker.Status = UploadStatus.Failed;
                        tracker.Message = e?.InnerException?.Message ?? e?.Message;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
        }
        private async Task UpdateDuplicateLeadsAsync(BulkMigrateBackgroundDto dto, List<Project> projects, List<Property> properties, List<InvalidData> invalids, InputPayload input)
        {

            List<Lead> leadsToUpdate = new();
            LeadMigrateTracker? tracker = await _leadMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);

            try
            {
                if (dto.Leads?.Any() ?? false)
                {
                    foreach (var lead in dto.Leads)
                    {
                        var LatestUpdatedLead = (await _dapperRepository.GetLeadDetails(lead.ContactNo));
                        var duplicateLatestLeadToUpdate = await _leadRepo.ListAsync(new GetLeadByDuplicateNoSpec(LatestUpdatedLead));

                        var duplicateLeadsToUpdate = await _leadRepo.ListAsync(new GetLeadByContactNoSpec(lead.ContactNo ?? string.Empty));
                        var invalidLead = lead.Adapt<InvalidData>();
                        if (tracker.MigrationType == LeadMigrationType.UpdateMissingInformation)
                        {
                            if (tracker.MigrationType == LeadMigrationType.UpdateMissingInformation && tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                            {
                                var updatedLeads = await MapAllObjectsForUpdateMissingInformation(lead, duplicateLeadsToUpdate, projects, properties, dto);
                                if (updatedLeads.Count == 0 || updatedLeads == null)
                                {
                                    invalidLead.Errors = "Parent Lead Not Found";
                                    invalids.Add(invalidLead);
                                }
                                else
                                {
                                    leadsToUpdate.AddRange(updatedLeads);
                                }
                            }
                            else if (tracker.MigrationType == LeadMigrationType.UpdateMissingInformation && tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                            {
                                var updatedLeads = await MapAllObjectsForUpdateMissingInformation(lead, duplicateLatestLeadToUpdate, projects, properties, dto);
                                if (updatedLeads.Count == 0 || updatedLeads == null)
                                {
                                    invalidLead.Errors = "Child Lead Not Found";
                                    invalids.Add(invalidLead);
                                }
                                else
                                {
                                    leadsToUpdate.AddRange(updatedLeads);
                                }
                            }
                            else
                            {
                                var updatedLeads = await MapAllObjectsForUpdateMissingInformation(lead, duplicateLeadsToUpdate, projects, properties, dto);
                                leadsToUpdate.AddRange(updatedLeads);
                            }
                        }
                        if (tracker.MigrationType == LeadMigrationType.OverideExisitingLeadInformation)
                        {
                            if (tracker.MigrationType == LeadMigrationType.OverideExisitingLeadInformation && tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                            {
                                var updatedLeads = MapAllObjects(lead, duplicateLeadsToUpdate, projects, properties, dto);
                                if (updatedLeads.Count == 0 || updatedLeads == null)
                                {
                                    invalidLead.Errors = "Parent Lead Not Found";
                                    invalids.Add(invalidLead);
                                }
                                else
                                {
                                    leadsToUpdate.AddRange(updatedLeads);
                                }
                            }
                            else if (tracker.MigrationType == LeadMigrationType.OverideExisitingLeadInformation && tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                            {
                                var updatedLeads = MapAllObjects(lead, duplicateLatestLeadToUpdate, projects, properties, dto);
                                if (updatedLeads.Count == 0 || updatedLeads == null)
                                {
                                    invalidLead.Errors = "Child Lead Not Found";
                                    invalids.Add(invalidLead);
                                }
                                else
                                {
                                    leadsToUpdate.AddRange(updatedLeads);
                                }
                            }
                            else
                            {
                                var updatedLeads = MapAllObjects(lead, duplicateLeadsToUpdate, projects, properties, dto);
                                leadsToUpdate.AddRange(updatedLeads);
                            }
                        }
                    }
                    if (invalids.Any())
                    {
                        tracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Lead").Count();
                        tracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty" || i.Errors == "Parent Lead Not Found" || i.Errors == "Child Lead Not Found").Count();
                        byte[] bytes = CreateLeadHelper.CreateExcelData(invalids).ToArray();
                        string fileName = $"InvalidLeads-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                        string folder = $"{input.TenantId}/Leads/Migrate";
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", folder, fileName, bytes) ?? string.Empty;
                        //var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        tracker.InvalidDataS3BucketKey = key;
                    }
                    try
                    {
                        await _leadRepo.UpdateRangeAsync(leadsToUpdate);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                        if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                        {
                            tracker.Status = UploadStatus.Completed;
                        }
                        else
                        {
                            tracker.Status = UploadStatus.Failed;
                            tracker.Message = e?.InnerException?.Message ?? e?.Message;
                        }
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateBulkLeadleadUploadTrackerUsingEPPlus -> ExecuteDBOperationsAsync()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    var leadDtos = leadsToUpdate.Adapt<List<ViewLeadDto>>();
                    dto.LeadDtos = leadDtos;
                    List<LeadHistory> leadHistories = new();
                    leadDtos.ForEach(leadDto =>
                    {
                        leadDto.SetUsersInViewLeadDto(dto.Users);
                        leadHistories.Add(LeadHistoryHelper.LeadHistoryMapper(leadDto));

                    });
                    if (leadHistories.Any())
                    {
                        await UpdateDuplicateLeadsHistoryAsync(leadHistories);
                    }
                    if (tracker != null)
                    {
                        tracker.TotalUploadedCount += dto.Leads.Count;
                        tracker.LastModifiedBy = dto.CurrentUserId;
                        tracker.CreatedBy = dto.CurrentUserId;
                        await _leadMigrateTrackerRepo.UpdateAsync(tracker);
                    }
                }
            
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == (tracker.DistinctLeadCount - tracker.InvalidCount - tracker.DuplicateCount))
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _leadMigrateTrackerRepo.UpdateAsync(tracker);
            }



           
        }
        private List<Lead> MapAllObjects(Lead src, List<Lead> destLeads, List<Project> projects, List<Property> properties, BulkMigrateBackgroundDto dto)
        {
            LeadMigrateTracker? tracker = _leadMigrateTrackerRepo.GetByIdAsync(dto.TrackerId).GetAwaiter().GetResult();
            foreach (var dest in destLeads)
            {
                if (dest.ParentLeadId == null && dest.RootId == null && tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                {
                    MappingMethodForOverrideExistingLeads(src, dest, projects, properties);
                }
                else if (tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                {
                    MappingMethodForOverrideExistingLeads(src, dest, projects, properties);
                }
                else if(tracker.MigrationSubType == LeadMigrationSubType.None)
                {
                    MappingMethodForOverrideExistingLeads(src, dest, projects, properties);
                }
            }
            return destLeads;
        }

        public static void MappingMethodForOverrideExistingLeads(Lead src, Lead dest, List<Project> projects, List<Property> properties)
        {
            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<LeadEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }
                /*                dest.Name = src.Name;
                                dest.ContactNo = src.ContactNo;*/


                dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email;
                dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes;
                dest.ConfidentialNotes = string.IsNullOrEmpty(src.ConfidentialNotes) ? dest.ConfidentialNotes : src.ConfidentialNotes;
                dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo;
                dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo;
                dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom;
             //  dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName;
                //est.Agencies = src.Agencies ?? dest.Agencies;
                dest.Agencies = src.Agencies ?? dest.Agencies;
                dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo;
                dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName;
                dest.CreatedBy = (src.CreatedBy == null || src.CreatedBy == Guid.Empty) ? dest.CreatedBy : src.CreatedBy;
                dest.CustomFlags = (src.CustomFlags == null || src.CustomFlags.Count == 0) ? dest.CustomFlags : src.CustomFlags;
                dest.LastModifiedBy = src.LastModifiedBy;
                dest.CreatedOn = src.CreatedOn;
                dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;

                dest.Notes = (string.IsNullOrEmpty(src.Notes)) ? dest.Notes : src.Notes;
                dest.ScheduledDate = (src.ScheduledDate == null) ? dest.ScheduledDate : src.ScheduledDate;
                dest.CustomLeadStatus = src.CustomLeadStatus ?? dest.CustomLeadStatus;
                dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                dest.Enquiries.FirstOrDefault().NoOfBHKs = src.Enquiries?.FirstOrDefault()?.NoOfBHKs != default ? src.Enquiries.FirstOrDefault().NoOfBHKs : dest.Enquiries[0].NoOfBHKs;
                dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                dest.Enquiries.FirstOrDefault().LeadSource = src.Enquiries?.FirstOrDefault()?.LeadSource != default ? src.Enquiries.FirstOrDefault().LeadSource : dest.Enquiries[0].LeadSource;
                dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                // dest.Enquiries.FirstOrDefault().BHKType = src.Enquiries?.FirstOrDefault()?.BHKType != default ? src.Enquiries.FirstOrDefault().BHKType : dest.Enquiries[0].BHKType;
                dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;


                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                // dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                dest.Enquiries.FirstOrDefault().OfferType = src.Enquiries?.FirstOrDefault()?.OfferType != default ? src.Enquiries.FirstOrDefault().OfferType : dest.Enquiries[0].OfferType;
                dest.Enquiries.FirstOrDefault().Purpose = src.Enquiries?.FirstOrDefault()?.Purpose != default ? src.Enquiries.FirstOrDefault().Purpose : dest.Enquiries[0].Purpose;


                if (dest.Appointments == null)
                {
                    dest.Appointments = new List<LeadAppointment>();
                }
                for (int i = 0; i < src.Appointments.Count; i++)
                {
                    if (i < dest.Appointments.Count)
                    {
                        dest.Appointments[i].Type = src.Appointments[i].Type;
                        dest.Appointments[i].IsDone = src.Appointments[i].IsDone;
                    }
                    else
                    {
                        dest.Appointments.Add(new LeadAppointment
                        {
                            Type = src.Appointments[i].Type,
                            IsDone = src.Appointments[i].IsDone
                        });
                    }
                }
                // dest.Appointments = src.Appointments;
                dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                dest.UploadType = src?.UploadType ?? dest.UploadType;
                dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;



            }
            catch
            {

            }
        }

            public async  Task<List<Lead>> MapAllObjectsForUpdateMissingInformation(Lead src, List<Lead> destLeads, List<Project> projects, List<Property> properties, BulkMigrateBackgroundDto dto)
            {
            LeadMigrateTracker? tracker =await _leadMigrateTrackerRepo.GetByIdAsync(dto.TrackerId);
            foreach (var dest in destLeads)
            {
                if (dest.ParentLeadId == null && dest.RootId == null && tracker.MigrationSubType == LeadMigrationSubType.ParentLead)
                {
                    MappingMethodForUpdateMissingInfo(src, dest, projects, properties);
                }
                else if (tracker.MigrationSubType == LeadMigrationSubType.LatestChildLead)
                {
                    MappingMethodForUpdateMissingInfo(src, dest, projects, properties);
                }
                else if(tracker.MigrationSubType == LeadMigrationSubType.None)
                {
                    MappingMethodForUpdateMissingInfo(src, dest, projects, properties);
                }
            }
            return destLeads;
        }

        public static void MappingMethodForUpdateMissingInfo(Lead src, Lead dest, List<Project> projects, List<Property> properties)
        {

            if (!dest.Enquiries.Any())
            {
                dest.Enquiries = new List<LeadEnquiry>();
            }
            try
            {
                if (src.Projects?.Any() ?? false)
                {
                    dest.Projects = new List<Project>() { projects.FirstOrDefault(i => i.Name == src.Projects?.FirstOrDefault()?.Name) ?? new() };
                }
                if (src.Properties?.Any() ?? false)
                {
                    dest.Properties = new List<Property>() { properties.FirstOrDefault(i => i.Title == src.Properties?.FirstOrDefault()?.Title) ?? new() };
                }


                /*                dest.Name = src.Name;
                                dest.ContactNo = src.ContactNo;*/

                if (dest.Email == null || dest.Email == string.Empty)
                { dest.Email = string.IsNullOrEmpty(src.Email) ? dest.Email : src.Email; }
                if (dest.Notes == null) { dest.Notes = string.IsNullOrEmpty(src.Notes) ? dest.Notes : src.Notes; }
                if (dest.ConfidentialNotes == null) { dest.ConfidentialNotes = string.IsNullOrEmpty(src.ConfidentialNotes) ? dest.ConfidentialNotes : src.ConfidentialNotes; }
                if (string.IsNullOrEmpty(dest.AlternateContactNo)) { dest.AlternateContactNo = string.IsNullOrEmpty(src.AlternateContactNo) ? dest.AlternateContactNo : src.AlternateContactNo; }
                if (dest.AssignTo == null || dest.AssignTo == Guid.Empty) { dest.AssignTo = (src.AssignTo != default && src.AssignTo != null) ? src.AssignTo : dest.AssignTo; }
                if (dest.AssignedFrom == null || dest.AssignedFrom == Guid.Empty) { dest.AssignedFrom = (src.AssignedFrom != default && src.AssignedFrom != null) ? src.AssignedFrom : dest.AssignedFrom; }
                //if (dest.AgencyName == null) { dest.AgencyName = string.IsNullOrEmpty(src.AgencyName) ? dest.AgencyName : src.AgencyName; }
                //est.Agencies = src.Agencies ?? dest.Agencies;
                if (dest.Agencies == null || dest.Agencies.Count == 0) { dest.Agencies = src.Agencies ?? dest.Agencies; }
                if (dest.ReferralContactNo == null || dest.ReferralContactNo == string.Empty) { dest.ReferralContactNo = string.IsNullOrEmpty(src.ReferralContactNo) ? dest.ReferralContactNo : src.ReferralContactNo; }
                if (dest.ReferralName == null || dest.ReferralName == string.Empty) { dest.ReferralName = string.IsNullOrEmpty(src.ReferralName) ? dest.ReferralName : src.ReferralName; }
                if (dest.CreatedBy == null) { dest.CreatedBy = src.CreatedBy; }
                if (dest.ReferralEmail == null || dest.ReferralEmail == string.Empty)
                {
                    dest.ReferralEmail = string.IsNullOrEmpty(src.ReferralEmail) ? dest.ReferralEmail : src.ReferralEmail;
                }
                    if (dest.CustomFlags == null || dest.CustomFlags.Count == 0) { dest.CustomFlags = src.CustomFlags; }
                    if (dest.LastModifiedBy == null) { dest.LastModifiedBy = src.LastModifiedBy; }
                    if (string.IsNullOrEmpty(dest.Notes))
                    {
                        dest.Notes = src.Notes;
                    }
                    if (dest.ScheduledDate == null) { dest.ScheduledDate = src.ScheduledDate; }
                    if (dest.CreatedOn == null) { dest.CreatedOn = src.CreatedOn; }
                    if (dest.CustomLeadStatus == null) { dest.CustomLeadStatus = src.CustomLeadStatus; }
                    // if (dest.Enquiries.FirstOrDefault().NoOfBHKs == null) { dest.Enquiries.FirstOrDefault().NoOfBHKs = src.Enquiries?.FirstOrDefault()?.NoOfBHKs != default ? src.Enquiries.FirstOrDefault().NoOfBHKs : dest.Enquiries[0].NoOfBHKs; }
                    if (dest.Enquiries.FirstOrDefault().PropertyType == null)
                    {
                        dest.Enquiries.FirstOrDefault().PropertyType = src.Enquiries?.FirstOrDefault()?.PropertyType != default ? src.Enquiries?.FirstOrDefault()?.PropertyType : dest.Enquiries[0].PropertyType;
                    }
                    if (dest.Enquiries.FirstOrDefault().BHKs == null || dest.Enquiries.FirstOrDefault().BHKs.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().BHKs = src.Enquiries?.FirstOrDefault()?.BHKs != default ? src.Enquiries?.FirstOrDefault()?.BHKs : dest.Enquiries[0].BHKs;
                    }
                    // dest.Enquiries.FirstOrDefault().EnquiredFor = src.Enquiries?.FirstOrDefault()?.EnquiredFor != default ? src.Enquiries.FirstOrDefault().EnquiredFor : dest.Enquiries[0].EnquiredFor;
                    if (dest.Enquiries.FirstOrDefault().EnquiryTypes == null || dest.Enquiries.FirstOrDefault().EnquiryTypes.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().EnquiryTypes = src.Enquiries?.FirstOrDefault()?.EnquiryTypes != default ? src.Enquiries?.FirstOrDefault()?.EnquiryTypes : dest.Enquiries[0].EnquiryTypes;
                    }
                    if (dest.Enquiries.FirstOrDefault().SaleType == null)
                    {
                        dest.Enquiries.FirstOrDefault().SaleType = dest.Enquiries[0].SaleType;
                    }
                    if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().SubSource))
                    {
                        dest.Enquiries.FirstOrDefault().SubSource = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.SubSource) ? dest.Enquiries[0].SubSource : src.Enquiries?.FirstOrDefault()?.SubSource;
                    }
                    if (dest.Enquiries.FirstOrDefault().LeadSource == null)
                    {
                        dest.Enquiries.FirstOrDefault().LeadSource = src.Enquiries?.FirstOrDefault()?.LeadSource != default ? src.Enquiries.FirstOrDefault().LeadSource : dest.Enquiries[0].LeadSource;
                    }
                    if (dest.Enquiries.FirstOrDefault().LowerBudget == null || dest.Enquiries.FirstOrDefault().LowerBudget == 0)
                    {
                        dest.Enquiries.FirstOrDefault().LowerBudget = (src.Enquiries?.FirstOrDefault()?.LowerBudget != default && src.Enquiries?.FirstOrDefault()?.LowerBudget != null) ? src.Enquiries?.FirstOrDefault()?.LowerBudget : dest.Enquiries[0].LowerBudget;
                    }
                    if (dest.Enquiries.FirstOrDefault().UpperBudget == null || dest.Enquiries.FirstOrDefault().UpperBudget == 0)
                    {
                        dest.Enquiries.FirstOrDefault().UpperBudget = (src.Enquiries?.FirstOrDefault()?.UpperBudget != default && src.Enquiries?.FirstOrDefault()?.UpperBudget != null) ? src.Enquiries?.FirstOrDefault()?.UpperBudget : dest.Enquiries[0].UpperBudget;
                    }
                    //if (dest.Enquiries.FirstOrDefault().BHKType == null || dest.Enquiries.FirstOrDefault().BHKType ==0) { dest.Enquiries.FirstOrDefault().BHKType = src.Enquiries?.FirstOrDefault()?.BHKType != default ? src.Enquiries.FirstOrDefault().BHKType : dest.Enquiries[0].BHKType; }
                    if (dest.Enquiries.FirstOrDefault().BHKTypes == null || dest.Enquiries.FirstOrDefault().BHKTypes.Count == 0)
                    {
                        dest.Enquiries.FirstOrDefault().BHKTypes = src.Enquiries?.FirstOrDefault()?.BHKTypes != default ? src.Enquiries?.FirstOrDefault()?.BHKTypes : dest.Enquiries[0].BHKTypes;
                    }
                    try
                    {
                        dest.Enquiries.FirstOrDefault().Address.City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.City) ? dest.Enquiries[0]?.Address?.City : src.Enquiries?.FirstOrDefault()?.Address?.City;
                        dest.Enquiries.FirstOrDefault().Address.State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.State) ? dest.Enquiries[0]?.Address?.State : src.Enquiries?.FirstOrDefault()?.Address?.State;
                        dest.Enquiries.FirstOrDefault().Address.Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Address?.Country) ? dest.Enquiries[0]?.Address?.Country : src.Enquiries?.FirstOrDefault()?.Address?.Country;
                    }
                    catch
                    {

                    }
                    if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality == string.Empty)
                    {
                        dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().SubLocality = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.SubLocality : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.SubLocality;
                    }
                    if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City == string.Empty)
                    {
                        dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().City = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.City : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.City;
                    }
                    if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State == string.Empty)
                    {
                        dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().State = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.State : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.State;
                    }
                    if (dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country == null || dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country == string.Empty)
                    {
                        dest.Enquiries.FirstOrDefault().Addresses.FirstOrDefault().Country = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country) ? dest.Enquiries[0]?.Addresses?.FirstOrDefault()?.Country : src.Enquiries.FirstOrDefault()?.Addresses?.FirstOrDefault()?.Country;
                    }
                    if (dest.Appointments == null || dest.Appointments.Count == 0)
                    {
                        dest.Appointments = new List<LeadAppointment>();
                    }
                    for (int i = 0; i < src.Appointments.Count; i++)
                    {
                        if (i < dest.Appointments.Count)
                        {
                            dest.Appointments[i].Type = src.Appointments[i].Type;
                            dest.Appointments[i].IsDone = src.Appointments[i].IsDone;
                        }
                        else
                        {
                            dest.Appointments.Add(new LeadAppointment
                            {
                                Type = src.Appointments[i].Type,
                                IsDone = src.Appointments[i].IsDone
                            });
                        }
                    }
                    // dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerNam
                    dest.ChannelPartnerName = string.IsNullOrEmpty(src.ChannelPartnerName) ? dest.ChannelPartnerName : src.ChannelPartnerName;
                    dest.ChannelPartnerExecutiveName = string.IsNullOrEmpty(src.ChannelPartnerExecutiveName) ? dest.ChannelPartnerExecutiveName : src.ChannelPartnerExecutiveName;
                    dest.ChannelPartnerContactNo = string.IsNullOrEmpty(src.ChannelPartnerContactNo) ? dest.ChannelPartnerContactNo : src.ChannelPartnerContactNo;
                    dest.UploadType = src?.UploadType ?? dest.UploadType;
                    dest.UploadTypeName = string.IsNullOrEmpty(src.UploadTypeName) ? dest.UploadTypeName : src.UploadTypeName;

                if (string.IsNullOrEmpty(dest.Enquiries.FirstOrDefault().ClusterName))
                {
                    dest.Enquiries.FirstOrDefault().ClusterName = string.IsNullOrEmpty(src.Enquiries?.FirstOrDefault()?.ClusterName) ? dest.Enquiries[0].ClusterName : src.Enquiries?.FirstOrDefault()?.ClusterName;
                }
                if (dest.Nationality == null || dest.Nationality == string.Empty)
                {
                    dest.Nationality = string.IsNullOrEmpty(src.Nationality) ? dest.Nationality : src.Nationality;
                }


            }
            catch
            {

            }
        }


        public async Task<(string ContactNo, string? AltContactNo)> ValidateLeadMigrateContactNoAsync(string contactNum, string? alternateContactNum, int countryCode, int alternativecountryCode, GlobalSettings globalSettingInfo, List<Lead> leads, List<InvalidData> invalids, string Name, CancellationToken cancellationToken = default)
        {
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettingInfo?.CountriesInfo);

            string contactNo = Regex.Replace(contactNum, "[^0-9]", "");
            string alternateContactNo = Regex.Replace(alternateContactNum, "[^0-9]", "");
            try
            {

                string altContactWithCountryCode = null;

                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countryCode, new List<string>());
                string defaultRegion = regionCodes.FirstOrDefault();
                if (defaultRegion == null)
                {
                    defaultRegion = countries?.FirstOrDefault()?.Code ?? "IN";
                }
                PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");

                string altNumericMobileNumber = string.Empty;
                PhoneNumber phoneNumberAltcontNo = null;
                string altContactWithCountryCode1 = string.Empty;
                if (alternateContactNo != string.Empty && alternativecountryCode != 0)
                {
                    List<string> regionCodesforaltcontactNo = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(alternativecountryCode, new List<string>());
                    string defaultRegionforAltContNo = regionCodesforaltcontactNo.FirstOrDefault();
                    if (defaultRegionforAltContNo == null)
                    {
                        defaultRegionforAltContNo = countries?.FirstOrDefault()?.Code ?? "IN";
                    }
                    try
                    {
                        phoneNumberAltcontNo = phoneUtil.Parse(alternateContactNo, defaultRegionforAltContNo);

                        PhoneNumber numberExamplealtConctNum = phoneUtil.GetExampleNumberForType(defaultRegionforAltContNo, PhoneNumberType.MOBILE);
                        string formattedNumberaltContNo = phoneUtil.Format(numberExamplealtConctNum, PhoneNumberFormat.E164);
                        altContactWithCountryCode = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                        altNumericMobileNumber = Regex.Replace(formattedNumberaltContNo, @"\D", "");
                    }
                    catch
                    {
                    }
                }
                bool isValid;
                if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
                {
                    isValid = true;
                    try
                    {
                        foreach (var lead in leads)
                        {
                            if (Regex.Replace(lead.ContactNo, "[^0-9]", "") == contactNo && lead.Name == Name)
                            {
                                lead.ContactNo = contactWithCountryCode;
                                if (string.IsNullOrWhiteSpace(lead.Name))
                                {

                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidLead);
                                }

                            }
                            if (!string.IsNullOrWhiteSpace(alternateContactNo) && (altNumericMobileNumber.Length == altContactWithCountryCode.Length - 1))
                            {
                                if (Regex.Replace(lead.AlternateContactNo, "[^0-9]", "") == alternateContactNo && lead.Name == Name)
                                {
                                    lead.AlternateContactNo = altContactWithCountryCode;
                                }
                            }
                        }
                    }
                    catch
                    {

                    }
                }
                else if ((contactNo.Length > 6 && contactNo.Length <= 20))
                {
                    isValid = true;

                    try
                    {
                        foreach (var lead in leads)
                        {
                            if (Regex.Replace(lead.ContactNo, "[^0-9]", "") == contactNo && lead.Name == Name)
                            {
                                if (contactNum.StartsWith("+"))
                                {
                                    lead.ContactNo = "+" + contactNo;
                                }
                                else
                                {
                                    lead.ContactNo = "+" + countryCode + contactNo;

                                }
                                if (string.IsNullOrEmpty(lead.Name))
                                {

                                    var invalidLead = lead.Adapt<InvalidData>();
                                    invalidLead.Errors = "Invalid ContactNo";
                                    invalids.Add(invalidLead);
                                }

                            }
                            if (!string.IsNullOrWhiteSpace(alternateContactNo) && (alternateContactNo.Length > 6 && alternateContactNo.Length < 20))
                            {
                                if (Regex.Replace(lead.AlternateContactNo, "[^0-9]", "") == alternateContactNo && lead.Name == Name)
                                {
                                    lead.AlternateContactNo = altContactWithCountryCode;
                                }
                            }
                        }
                    }
                    catch
                    {

                    }
                }
                else
                {
                    isValid = false;
                    try
                    {
                        foreach (var lead in leads)
                        {
                            if (Regex.Replace(lead.ContactNo, "[^0-9]", "") == contactNo && lead.Name == Name)
                            {
                                lead.ContactNo = contactNum;
                                var invalidLead = lead.Adapt<InvalidData>();
                                invalidLead.Errors = "Invalid ContactNo";
                                invalids.Add(invalidLead);
                            }
                            if (!string.IsNullOrWhiteSpace(alternateContactNo))
                            {
                                if (Regex.Replace(lead.AlternateContactNo, "[^0-9]", "") == alternateContactNo && lead.Name == Name)
                                {
                                    lead.AlternateContactNo = alternateContactNum;

                                }
                            }

                        }
                    }
                    catch
                    {

                    }
                }

                if (!isValid)
                {
                    return (string.Empty, string.Empty);
                }
                else
                {
                    if (phoneUtil.GetRegionCodeForNumber(phoneNumber) != defaultRegion)
                    {
                        throw new Exception("Invalid ContactNo - International numbers not allowed");
                    }
                    string contactWithCountryCode1 = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                    if (phoneNumberAltcontNo != null)
                    {
                        altContactWithCountryCode1 = phoneUtil.Format(phoneNumberAltcontNo, PhoneNumberFormat.E164);
                    }
                    return (contactWithCountryCode1, altContactWithCountryCode1);
                }
            }
            catch (Exception ex)
            {

                foreach (var lead in leads)
                {
                    if (Regex.Replace(lead.ContactNo, "[^0-9]", "") == contactNo && lead.Name == Name)
                    {
                        lead.ContactNo = contactNum;
                        var invalidLead = lead.Adapt<InvalidData>();
                        invalidLead.Errors = "Invalid ContactNo";
                        invalids.Add(invalidLead);
                    }
                    if (!string.IsNullOrWhiteSpace(alternateContactNum))
                    {
                        if (Regex.Replace(lead.AlternateContactNo, "[^0-9]", "") == alternateContactNo && lead.Name == Name)
                        {
                            lead.AlternateContactNo = alternateContactNum;
                        }
                    }

                }

                throw;
            }
        }
    }
}
