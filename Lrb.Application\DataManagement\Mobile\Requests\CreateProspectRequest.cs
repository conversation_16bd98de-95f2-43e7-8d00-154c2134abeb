﻿using Lrb.Application.ChannelPartner.Mobile.Specs;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Mapping;
using Lrb.Application.DataManagement.Mobile.Requests.CommonHandler;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Property.Mobile;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.Globalization;
namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class CreateProspectRequest : CreateProspectDto, IRequest<Response<Guid>>
    {
    }

    public class CreateProspectRequestHandler : DataCommonRequestHandler, IRequestHandler<CreateProspectRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUserRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _channelPartnerRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospcetHistoryRepo;
        private readonly IUserService _userService;

        public CreateProspectRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUserRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IGooglePlacesService googlePlacesService,
            IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> globalSettingRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> channelPartnerRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
             IRepositoryWithEvents<ProspectHistory> prospcetHistoryRepo,
            IUserService userService,
            IServiceProvider serviceProvider
            ) : base(serviceProvider)
        {
            _prospectRepo = prospectRepo;
            _leadRepo = leadRepo;
            _currentUserRepo = currentUserRepo;
            _globalSettingRepo = globalSettingRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _propertyRepo = propertyRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _channelPartnerRepo = channelPartnerRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _userService = userService;
        }
        public async Task<Response<Guid>> Handle(CreateProspectRequest request, CancellationToken cancellationToken)
        {
            bool isValidContactNo = false;
            var globalSetting = await _globalSettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var hasInternationSupportEnabled = globalSetting?.HasInternationalSupport ?? false;
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSetting?.CountriesInfo);


            var currency= countries?.FirstOrDefault()?.DefaultCurrency;
            if (request?.ContactNo?.Length == 10 && !hasInternationSupportEnabled)
            {
                request.ContactNo = $"+91{request.ContactNo.Trim()}";
            }
            isValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(request?.ContactNo, hasInternationSupportEnabled);
            if (!isValidContactNo)
            {
                throw new InvalidDataException("Invalid Contact");
            }
            if (request.AlternateContactNo != null)
            {
                if (request?.AlternateContactNo?.Length == 10 && !hasInternationSupportEnabled)
                {
                    request.AlternateContactNo = $"+91{request.AlternateContactNo.Trim()}";
                }
            }

            var currentUserId = _currentUserRepo.GetUserId();
            //var existingData = (await _prospectRepo.ListAsync(new GetProspectByContactNoSpecs(request.ContactNo), cancellationToken)).FirstOrDefault();
            //if (existingData != null)
            //{
            //    throw new Exception($"Duplicate Prospect {JsonConvert.SerializeObject(existingData)}");
            //}
            List<MasterPropertyType>? propertyTypes = null;
            if (request.Enquiry?.PropertyTypeIds?.Any() ?? false)
            {
                propertyTypes = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.Enquiry?.PropertyTypeIds), cancellationToken);

            }
            MasterProspectSource? source = null;
            if (request.Enquiry?.ProspectSourceId != Guid.Empty && request.Enquiry?.ProspectSourceId != null)
            {
                source = await _prospectSourceRepo.GetByIdAsync(request.Enquiry.ProspectSourceId, cancellationToken);
                if (source == null)
                {
                    throw new NotFoundException("No Source found by this Id");
                }
            }
            else
            {
                source = await _prospectSourceRepo.FirstOrDefaultAsync(new GetDefaultProspectSourceSpecs());
            }
            var prospect = request.Adapt<Prospect>();

            Address? address = await CreateAddressAsync(request.Enquiry?.Address, cancellationToken);
            List<Address>? addresses = new();
            if (request.Enquiry?.Addresses?.Any() ?? false)
            {
                foreach (var enquiryAddress in request.Enquiry.Addresses)
                {
                    var ValidatedAddress = await CreateAddressAsync(enquiryAddress, cancellationToken);
                    if (ValidatedAddress != null)
                        addresses.Add(ValidatedAddress);
                }
            }

            Address? prospectAddress = null;
            if (request?.AddressDto != null)
            {
                prospectAddress = await CreateAddressAsync(request.AddressDto, cancellationToken);
            }
            var status = await _prospectStatusRepo.FirstOrDefaultAsync(new GetDefaultProspectStatusSpecs());
            prospect.Status = status;
            prospect.Address = prospectAddress;
            var prospectEnquiry = request?.Enquiry?.Adapt<ProspectEnquiry>() ?? new();
            prospectEnquiry.IsPrimary = true;
            prospectEnquiry.Address = address;
            prospectEnquiry.Addresses = addresses;
            if (prospectEnquiry.Address == null)
            {
                prospectEnquiry.AddressId = null;
            }
            prospectEnquiry.Source = source;
            if (propertyTypes != null)
            {
                prospectEnquiry.PropertyTypes = propertyTypes;
                prospectEnquiry.PropertyType = propertyTypes?.FirstOrDefault();
            }
            if (request?.Enquiry?.CarpetArea != null)
            {
                prospectEnquiry.CarpetAreaInSqMtr = (request.Enquiry.CarpetArea * request.Enquiry.ConversionFactor);
            }
            if (request?.Enquiry?.BuiltUpArea != null)
            {
                prospectEnquiry.BuiltUpAreaInSqMtr = (request.Enquiry.BuiltUpArea * request.Enquiry.BuiltUpAreaConversionFactor);
            }
            if (request?.Enquiry?.SaleableArea != null)
            {
                prospectEnquiry.SaleableAreaInSqMtr = (request.Enquiry.SaleableArea * request.Enquiry.SaleableAreaConversionFactor);
            }
            if (request?.Enquiry?.NetArea != null)
            {
                prospectEnquiry.NetAreaInSqMtr = (request.Enquiry.NetArea * request.Enquiry.NetAreaConversionFactor);
            }
            if (request?.Enquiry?.PropertyArea != null)
            {
                prospectEnquiry.PropertyAreaInSqMtr = (request.Enquiry.PropertyArea * request.Enquiry.PropertyAreaConversionFactor);
            }
            prospect.Enquiries = new List<ProspectEnquiry>();
            if (request?.AssignTo == default || request?.AssignTo == Guid.Empty)
            {
                prospect.AssignTo = currentUserId;
                //prospect.AssignedFrom = currentUserId;
            }
            else
            {
                //prospect.AssignedFrom = currentUserId;
                prospect.AssignTo = request?.AssignTo ?? Guid.Empty;
            }
            prospect.Enquiries.Add(prospectEnquiry);

            await SetProspectAgenciesAsync(prospect, request?.Agencies, cancellationToken);
            #region Property
            List<Domain.Entities.Property>? properties = new();
            request.PropertiesList = (request.PropertiesList?.Any() ?? false) ? request.PropertiesList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            if (request.PropertiesList?.Any() ?? false)
            {
                foreach (var newProperty in request.PropertiesList)
                {
                    var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                    if (existingProperty != null)
                    {
                        properties.Add(existingProperty);
                    }
                    else
                    {
                        Domain.Entities.Property property = new()
                        {
                            Title = newProperty,
                            MonetaryInfo = new PropertyMonetaryInfo
                            {
                                Currency = currency ?? "INR"
                            }
                        };
                        property = await _propertyRepo.AddAsync(property, cancellationToken);
                        properties.Add(property);
                    }
                }
                prospect.Properties = properties;
            }
            #endregion

            await SetProspectProjectAsync(prospect, request.ProjectsList, globalSetting, cancellationToken);

            #region Channel Partners
            List<Lrb.Domain.Entities.ChannelPartner> channelPartners = new();
            request.ChannelPartnerList = (request.ChannelPartnerList?.Any() ?? false) ? request.ChannelPartnerList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            if (request.ChannelPartnerList?.Any() ?? false)
            {
                foreach (var newChannelPartner in request.ChannelPartnerList)
                {
                    var existingChannelPartner = (await _channelPartnerRepo.ListAsync(new GetChannelPartnerByNameSpecs(newChannelPartner), cancellationToken)).FirstOrDefault();
                    if (existingChannelPartner != null)
                    {
                        channelPartners.Add(existingChannelPartner);
                    }
                    else
                    {
                        Lrb.Domain.Entities.ChannelPartner channelPartner = new() { FirmName = newChannelPartner };
                        channelPartner = await _channelPartnerRepo.AddAsync(channelPartner, cancellationToken);
                        channelPartners.Add(channelPartner);
                    }
                }
                prospect.ChannelPartners = channelPartners;
            }
            #endregion
            await SetProspectCampaignAsync(prospect, request?.Campaigns, cancellationToken);
            prospect = await _prospectRepo.AddAsync(prospect);

            #region History
            var statuses = await _prospectStatusRepo.ListAsync();
            var propertyTypeses = await _propertyTypeRepo.ListAsync();
            var prospectVM = prospect.Adapt<ViewProspectDto>();
            var userIds = new List<string?>
            {
                   prospectVM.AssignTo.ToString(),
                   prospectVM.LastModifiedBy.ToString(),
                   prospectVM.AssignedFrom.ToString(),
                   prospectVM.SourcingManager.ToString(),
                   prospectVM.ClosingManager.ToString(),
                   currentUserId.ToString(),
            };
            var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);

            prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
            var histories = await ProspectHistoryHelper.CreateProspectHistoryForVM(prospectVM, null, currentUserId, 1, statuses, propertyTypeses, null, _userService, cancellationToken);
            await _prospcetHistoryRepo.AddRangeAsync(histories);
            #endregion
            return new Response<Guid>(prospect.Id);
        }
    }
}
