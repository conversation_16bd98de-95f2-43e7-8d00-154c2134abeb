﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Microsoft.EntityFrameworkCore.Query.SqlExpressions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Graph;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Web.Request.CommonHandler
{
    public class DataCommonRequestHandler
    {
        private readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<Domain.Entities.Location> _locationRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        private readonly IProspectRepository _efProspectRepository;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Property> _propertyRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _channelPartnerRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        private readonly ICurrentUser _currentUser;
        protected readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IUserService _userService;
        protected readonly IRepositoryWithEvents<ProspectHistory> _prospcetHistoryRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Campaign> _campaignRepo;

        public DataCommonRequestHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Location>>(); ;
            _mediator = _serviceProvider.GetRequiredService<IMediator>(); ;
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _efProspectRepository = _serviceProvider.GetRequiredService<IProspectRepository>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.Project>>();
            _propertyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.Property>>();
            _channelPartnerRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _agencyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Agency>>();
            _currentUser = _serviceProvider.GetRequiredService<ICurrentUser>();
            _prospectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Prospect>>();
            _prospectStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomProspectStatus>>();
            _prospectSourceRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterProspectSource>>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _prospcetHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<ProspectHistory>>();
            _campaignRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Campaign>>();
        }


        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = location.MapToAddress();
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {

                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {

                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                {

                    if (newAddress != null)
                    {
                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                        if (existingAddress == null)
                        {
                            address = await _addressRepo.AddAsync(newAddress);
                        }
                        else
                        {
                            address = existingAddress;
                        }
                        await MapAddressToLocationAndSaveAsync(address);
                    }

                }
                return address;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            try
            {
                var location = address.MapToLocationRequest();
                if (location != null)
                {
                    var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                    var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                    address.Location = createdLocation;
                    await _addressRepo.UpdateAsync(address);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<ProspectCountByFilterDto> AddBaseProspectCount(ProspectCountByFilterDto prospectCount, string propertyName, GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            switch (propertyName)
            {
                case nameof(ProspectCountByFilterDto.ManageDataCount):
                    request.ProspectVisiblity = (ProspectVisiblity)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.ManageDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.MyDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Self;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.MyDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.TeamDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.Reportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.TeamDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.AllDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.AllDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.UnAssignedCount):
                    request.ProspectVisiblity = ProspectVisiblity.UnassignData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.UnAssignedCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.DeletedDataCount):
                    request.ProspectVisiblity = ProspectVisiblity.DeletedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.DeletedDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NewDataCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    prospectCount.NewDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.ActiveDataCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    prospectCount.ActiveDataCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.FollowUpsCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Followups;
                    prospectCount.FollowUpsCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.BacklogCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Backlog;
                    prospectCount.BacklogCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotInterestedCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.NotInterested;
                    prospectCount.NotInterestedCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotReachableCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotReachable;
                    prospectCount.NotReachableCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.InvalidCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.InValid;
                    prospectCount.InvalidCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.InactiveCount):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    prospectCount.InactiveCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.NotAnsweredCount):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotAnswered;
                    prospectCount.NotAnsweredCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.QualifiedCount):
                    request.FirstLevelFilter = FirstLevelFilter.Qualified;
                    prospectCount.QualifiedCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByFilterDto.ConvertedCount):
                    request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    prospectCount.ConvertedCount = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
            }
            return prospectCount;
        }

        //FirstLevel filter & SecondLevel filter
        public async Task<(long, int, int)> AddrospectCount(ProspectCountByNewTopLevelFilterDto prospectCount, System.Reflection.PropertyInfo property, GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            var propertyName = property.Name;
            long count = 0;
            switch (propertyName)
            {
                case nameof(ProspectCountByNewTopLevelFilterDto.ManageDataCount):
                    request.ProspectVisiblity = (ProspectVisiblity)(-1);
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.MyData):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.Self : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Team):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.Reportee : request.ProspectVisiblity; ;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.AllData):
                    //  request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.SelfWithReportee : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Unassigned):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.UnassignData : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.DeletedData):
                    request.ProspectVisiblity = request.ProspectVisiblity != ProspectVisiblity.UnassignData && request.ProspectVisiblity != ProspectVisiblity.DeletedData ? ProspectVisiblity.DeletedData : request.ProspectVisiblity;
                    request.FirstLevelFilter = FirstLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.New):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.New;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.ActiveData):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Followups):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Followups;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Backlog):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.Backlog;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotInterested):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.NotInterested;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotReachable):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotReachable;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Invalid):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.InValid;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Inactive):
                    request.FirstLevelFilter = FirstLevelFilter.Inactive;
                    request.SecondLevelFilter = SecondLevelFilter.All;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.NotAnswered):
                    request.FirstLevelFilter = FirstLevelFilter.Active;
                    request.SecondLevelFilter = SecondLevelFilter.NotAnswered;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
                case nameof(ProspectCountByNewTopLevelFilterDto.Qualified):
                    request.FirstLevelFilter = FirstLevelFilter.Qualified;
                    count = (await _efProspectRepository.GetAllProspectCountForWeb(request, userId, subIds, statuses));
                    break;
            }
            return (count, (int)request.FirstLevelFilter, (int)request.SecondLevelFilter);
        }

        public async Task<List<ProspectCountByStatusFilterDto>> ReturProspectCountInStructure(GetProspectCountByCustomStatusFilterRequest? request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            ProspectCountByFilterDto prospectCount = new();
            var propertyName = typeof(ProspectCountByFilterDto).GetProperties().Select(i => i.Name);
            //foreach (var property in propertyName)
            //{
            //    prospectCount = await AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
            //}

            var tasks = propertyName.Select(property => AddBaseProspectCount(prospectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses));
            await Task.WhenAll(tasks);
            prospectCount = tasks.LastOrDefault()?.Result ?? prospectCount;


            List<ProspectCountByStatusFilterDto> result = new List<ProspectCountByStatusFilterDto>();
            var baseProperties = typeof(ProspectCountByBaseFilterDto).GetProperties().Select(i => i.Name);

            foreach (var property in baseProperties)
            {
                var statusFilterDto1 = new ProspectCountByStatusFilterDto();
                switch (property)
                {
                    case "AllDataCount":
                        if (request.ProspectVisiblity == ProspectVisiblity.SelfWithReportee)
                        {
                            statusFilterDto1.Count = prospectCount.AllDataCount;
                            statusFilterDto1.EnumValue = 0;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "All Data";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        else
                        {
                            request.ProspectVisiblity = ProspectVisiblity.SelfWithReportee;
                            statusFilterDto1.Count = prospectCount.AllDataCount;
                            statusFilterDto1.EnumValue = 0;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "All Data";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        break;

                    case "MyDataCount":

                        if (request.ProspectVisiblity == ProspectVisiblity.Self)
                        {
                            statusFilterDto1.Count = prospectCount.MyDataCount;
                            statusFilterDto1.EnumValue = 1;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "My Data";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/single-muso.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        else
                        {
                            request.ProspectVisiblity = ProspectVisiblity.Self;
                            statusFilterDto1.Count = prospectCount.MyDataCount;
                            statusFilterDto1.EnumValue = 1;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "My Data";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/single-muso.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        break;

                    case "TeamDataCount":
                        if (request.ProspectVisiblity == ProspectVisiblity.Reportee)
                        {
                            statusFilterDto1.Count = prospectCount.TeamDataCount;
                            statusFilterDto1.EnumValue = 2;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Team";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        else
                        {
                            request.ProspectVisiblity = ProspectVisiblity.Reportee;
                            statusFilterDto1.Count = prospectCount.TeamDataCount;
                            statusFilterDto1.EnumValue = 2;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Team";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/muso-team.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }

                        break;

                    case "ConvertedCount":
                        if (request.ProspectVisiblity == ProspectVisiblity.ConvertedData)
                        {
                            statusFilterDto1.Count = prospectCount.ConvertedCount;
                            statusFilterDto1.EnumValue = 3;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Converted";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/single-muso.svg";
                            statusFilterDto1.IsConverted = true;
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        else
                        {
                            request.ProspectVisiblity = ProspectVisiblity.ConvertedData;
                            statusFilterDto1.Count = prospectCount.ConvertedCount;
                            statusFilterDto1.EnumValue = 3;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Converted";
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/single-muso.svg";
                            statusFilterDto1.IsConverted = true;
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        break;

                    case "DeletedDataCount":
                        if (request.ProspectVisiblity == ProspectVisiblity.DeletedData)
                        {
                            statusFilterDto1.Count = prospectCount.DeletedDataCount;
                            statusFilterDto1.EnumValue = 4;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Deleted";
                            statusFilterDto1.IsDeleted = true;
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/delete.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        else
                        {
                            request.ProspectVisiblity = ProspectVisiblity.DeletedData;
                            statusFilterDto1.Count = prospectCount.DeletedDataCount;
                            statusFilterDto1.EnumValue = 4;
                            statusFilterDto1.Name = property;
                            statusFilterDto1.DisplayName = "Deleted";
                            statusFilterDto1.IsDeleted = true;
                            statusFilterDto1.FilterPayloadKey = "ProspectVisiblity";
                            statusFilterDto1.logoUrl = "assets/images/delete.svg";
                            statusFilterDto1.Children = await GetFirstAndSecondLevelFilterCountAsync(request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        }
                        break;
                }
                result.Add(statusFilterDto1);
            }
            return result;
        }

        public async Task<List<ProspectCountNewDto>> GetFirstAndSecondLevelFilterCountAsync(GetAllProspectRequest request, Guid userId, List<Guid> subIds, List<CustomProspectStatus> statuses)
        {
            List<ProspectCountNewDto> newProspectStatusFilterDto = new List<ProspectCountNewDto>();
            var properties = typeof(ProspectCountByNewFilterDto).GetProperties().ToList();
            ProspectCountByNewTopLevelFilterDto newProspectCount = new();

            foreach (var property in properties)
            {
                var filterDto = new ProspectCountNewDto();
                var statusFilterDto = new ProspectCountByStatusDto();
                List<ProspectCountNewDto> subStastusCounts = new();

                switch (property.Name)
                {
                    case "AllData":
                        var allCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                        filterDto.Count = allCount.Item1;
                        filterDto.EnumValue = 0;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "All Data";
                        filterDto.IsDefault = true;
                        filterDto.FilterPayloadKey = "FirstLevelFilter";
                        filterDto.Children = subStastusCounts;
                        break;

                    case "ActiveData":
                        var activeBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                        filterDto.Count = activeBaseCount.Item1;
                        filterDto.EnumValue = 1;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Active Data";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";

                        var activeFlags = typeof(ProspectActiveStatusCount).GetProperties().ToList();
                        var activeSubStastusCount = activeFlags.Select(async activeFlag =>
                        {
                            var activeCount = await AddrospectCount(newProspectCount, activeFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                            var displayName = ProspectHelper.InsertSpaceBeforeCapital(activeFlag.Name);

                            return new ProspectCountNewDto()
                            {
                                Count = activeCount.Item1,
                                EnumValue = activeCount.Item3,
                                Name = activeFlag.Name,
                                DisplayName = displayName,
                                FilterPayloadKey = "SecondLevelFilter"
                            };
                        }).Select(task => task.Result).ToList();

                        filterDto.Children = activeSubStastusCount;

                        break;


                    //case "ActiveData":
                    //    var activeBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                    //    filterDto.Count = activeBaseCount.Item1;
                    //    filterDto.EnumValue = 1;
                    //    filterDto.Name = property.Name;
                    //    filterDto.DisplayName = "Active Data";
                    //    filterDto.FilterPayloadKey = "FirstLevelFilter";

                    //    var activeFlags = typeof(ProspectActiveStatusCount).GetProperties().ToList();
                    //    foreach (var activeFlag in activeFlags)
                    //    {
                    //        var activeCount = await AddrospectCount(newProspectCount, activeFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                    //        var displayName = ProspectHelper.InsertSpaceBeforeCapital(activeFlag.Name);
                    //        subStastusCounts.Add(new ProspectCountNewDto() { Count = activeCount.Item1, EnumValue = activeCount.Item3, Name = activeFlag.Name, DisplayName = displayName, FilterPayloadKey = "SecondLevelFilter" });
                    //        filterDto.Children = subStastusCounts;
                    //    }
                    //    break;

                    case "Inactive":
                        var inactiveBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                        filterDto.Count = inactiveBaseCount.Item1;
                        filterDto.EnumValue = 3;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Inactive Data";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";

                        var inactiveFlags = typeof(ProspectInActiveStatusCount).GetProperties().ToList();

                        var inactiveSubStastusCount = inactiveFlags.Select(async inActiveFlag =>
                        {
                            var inctiveCount = await AddrospectCount(newProspectCount, inActiveFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                            var displayName = ProspectHelper.InsertSpaceBeforeCapital(inActiveFlag.Name);

                            return new ProspectCountNewDto()
                            {
                                Count = inctiveCount.Item1,
                                EnumValue = inctiveCount.Item3,
                                Name = inActiveFlag.Name,
                                DisplayName = displayName,
                                FilterPayloadKey = "SecondLevelFilter"
                            };
                        }).Select(task => task.Result).ToList();

                        filterDto.Children = inactiveSubStastusCount;

                        break;


                    //case "Inactive":
                    //    var inactiveBaseCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                    //    filterDto.Count = inactiveBaseCount.Item1;
                    //    filterDto.EnumValue = 3;
                    //    filterDto.Name = property.Name;
                    //    filterDto.DisplayName = "Inactive Data";
                    //    filterDto.FilterPayloadKey = "FirstLevelFilter";

                    //    var inactiveFlags = typeof(ProspectInActiveStatusCount).GetProperties().ToList();
                    //    foreach (var inActiveFlag in inactiveFlags)
                    //    {
                    //        var inctiveCount = await AddrospectCount(newProspectCount, inActiveFlag, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);
                    //        var displayName = ProspectHelper.InsertSpaceBeforeCapital(inActiveFlag.Name);
                    //        subStastusCounts.Add(new ProspectCountNewDto() { Count = inctiveCount.Item1, EnumValue = inctiveCount.Item3, Name = inActiveFlag.Name, DisplayName = displayName, FilterPayloadKey = "SecondLevelFilter" });
                    //        filterDto.Children = subStastusCounts;
                    //    }
                    //    break;

                    case "Qualified":
                        var qualifiedCount = await AddrospectCount(newProspectCount, property, request.Adapt<GetAllProspectRequest>(), userId, subIds, statuses);

                        filterDto.Count = qualifiedCount.Item1;
                        filterDto.EnumValue = 2;
                        filterDto.Name = property.Name;
                        filterDto.DisplayName = "Qualified";
                        filterDto.FilterPayloadKey = "FirstLevelFilter";
                        statusFilterDto.ChildCount = subStastusCounts;
                        break;
                }
                newProspectStatusFilterDto.Add(filterDto);
            }
            return newProspectStatusFilterDto;
        }


        public Lrb.Domain.Entities.Lead MapProspectToLead(Prospect prospect, CancellationToken cancellationToken)
        {
            Lrb.Domain.Entities.Lead lead = new()
            {
                Name = prospect?.Name?.Trim(),
                ContactNo = prospect?.ContactNo?.Trim(),
                AlternateContactNo = prospect?.AlternateContactNo?.Trim(),
                Notes = prospect?.Notes,
                Email = prospect?.Email,
                Address = new Address()
                {
                    SubLocality = prospect?.Address?.SubLocality,
                    Locality = prospect?.Address?.Locality,
                    PlaceId = prospect?.Address?.PlaceId,
                    City = prospect?.Address?.City,
                    District = prospect?.Address?.District,
                    State = prospect?.Address?.State,
                    Country = prospect?.Address?.Country,
                    TowerName = prospect?.Address?.TowerName,
                    Community = prospect?.Address?.Community,
                    SubCommunity = prospect?.Address?.SubCommunity,
                },
                AgencyName = prospect?.AgencyName?.Trim(),
                Agencies = prospect?.Agencies,
                ChannelPartners = prospect?.ChannelPartners,
                ClosingManager = prospect?.ClosingManager,
                SourcingManager = prospect?.SourcingManager,
                ContactRecords = prospect?.ContactRecords,
                CompanyName = prospect?.CompanyName,
                Properties = prospect?.Properties,
                Projects = prospect?.Projects,
                Profession = prospect.Profession,
                Designation = prospect?.Designation,
                ChannelPartnerExecutiveName = prospect?.ExecutiveName,
                ChannelPartnerContactNo = prospect?.ExecutiveContactNo,
                ReferralContactNo = prospect?.ReferralContactNo,
                ReferralEmail = prospect?.ReferralEmail,
                ReferralName = prospect?.ReferralName,
                Nationality = prospect?.Nationality,
                Campaigns = prospect?.Campaigns,
                PossesionType = prospect?.Enquiries?.FirstOrDefault()?.PossesionType,
                LandLine = prospect?.LandLine,
                CountryCode = prospect?.CountryCode,
                AltCountryCode = prospect?.AltCountryCode,
                Gender = prospect?.Gender,
                MaritalStatus = prospect?.MaritalStatus,
                DateOfBirth = prospect?.DateOfBirth,

            };
            lead.LeadNumber = lead?.Name?[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            return lead;
        }
        public Lrb.Domain.Entities.LeadEnquiry CreateLeadEnquiryFromProspectEnquiry(ProspectEnquiry enquiry, DateTime? possionDate)
        {
            var leadEnquiry = new LeadEnquiry();
            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);
            leadEnquiry.LeadSource = source ?? LeadSource.Direct;
            leadEnquiry.IsPrimary = true;
            var address = new Address()
            {
                SubLocality = enquiry?.Address?.SubLocality,
                Locality = enquiry?.Address?.Locality,
                PlaceId = enquiry?.Address?.PlaceId,
                City = enquiry?.Address?.City,
                District = enquiry?.Address?.District,
                State = enquiry?.Address?.State,
                Country = enquiry?.Address?.Country,
                TowerName = enquiry?.Address?.TowerName,
                Community = enquiry?.Address?.Community,
                SubCommunity = enquiry?.Address?.SubCommunity,
            };
            leadEnquiry.BHKs = enquiry?.BHKs;
            leadEnquiry.BHKTypes = enquiry?.BHKTypes;
            leadEnquiry.EnquiryTypes = enquiry?.EnquiryTypes;
            leadEnquiry.EnquiredFor = enquiry?.EnquiryType ?? EnquiryType.None;
            leadEnquiry.SubSource = enquiry?.SubSource?.ToLower();
            leadEnquiry.LowerBudget = enquiry?.LowerBudget;
            leadEnquiry.UpperBudget = enquiry?.UpperBudget;
            leadEnquiry.CarpetArea = enquiry?.CarpetArea;
            leadEnquiry.CarpetAreaInSqMtr = enquiry?.CarpetAreaInSqMtr;
            leadEnquiry.CarpetAreaUnitId = enquiry?.CarpetAreaUnitId ?? Guid.Empty;
            leadEnquiry.NoOfBHKs = enquiry?.NoOfBhks ?? default;
            leadEnquiry.Address = address;
            leadEnquiry.PossessionDate = possionDate;
            leadEnquiry.BuiltUpArea = enquiry?.BuiltUpArea;
            leadEnquiry.BuiltUpAreaUnitId = enquiry?.BuiltUpAreaUnitId ?? Guid.Empty;
            leadEnquiry.BuiltUpAreaInSqMtr = enquiry?.BuiltUpAreaInSqMtr;
            leadEnquiry.SaleableArea = enquiry?.SaleableArea;
            leadEnquiry.SaleableAreaInSqMtr = enquiry?.SaleableAreaInSqMtr;
            leadEnquiry.SaleableAreaUnitId = enquiry?.SaleableAreaUnitId ?? Guid.Empty;
            leadEnquiry.Beds = enquiry?.Beds;
            leadEnquiry.Baths = enquiry?.Baths;
            leadEnquiry.Floors = enquiry?.Floors;
            leadEnquiry.Furnished = enquiry?.Furnished;
            leadEnquiry.OfferType = enquiry?.OfferType;
            leadEnquiry.Currency = enquiry?.Currency;
            leadEnquiry.NetArea = enquiry?.NetArea;
            leadEnquiry.NetAreaInSqMtr = enquiry?.NetAreaInSqMtr;
            leadEnquiry.NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty;
            leadEnquiry.PropertyArea = enquiry?.PropertyArea;
            leadEnquiry.PropertyAreaInSqMtr = enquiry?.PropertyAreaInSqMtr;
            leadEnquiry.PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty;
            leadEnquiry.UnitName = enquiry?.UnitName;
            leadEnquiry.ClusterName = enquiry?.ClusterName;
            leadEnquiry.PropertyTypes = enquiry?.PropertyTypes;
            leadEnquiry.Purpose = enquiry?.Purpose;




            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var item in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = item?.SubLocality,
                        Locality = item?.Locality,
                        PlaceId = item?.PlaceId,
                        City = item?.City,
                        District = item?.District,
                        State = item?.State,
                        Country = item?.Country,
                        TowerName = item?.TowerName,
                        Community = item?.Community,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }
            return leadEnquiry;
        }
        public async Task<Lrb.Domain.Entities.LeadEnquiry> MapProspectEnquiryToLeadEnquiry(ProspectEnquiry? enquiry, CancellationToken cancellationToken)
        {

            List<MasterPropertyType>? propertyTypes = null;
            if (enquiry?.PropertyTypes != null)
            {
                propertyTypes = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(enquiry.PropertyTypes.Select(i => i.Id).ToList()));
            }

            LeadSource? source = EnumFromDescription.GetValueFromDescription<LeadSource>(enquiry?.Source.DisplayName ?? string.Empty);

            var leadEnquiry = new LeadEnquiry()
            {
                EnquiredFor = enquiry.EnquiryType,
                SubSource = enquiry.SubSource?.ToLower(),
                LowerBudget = enquiry.LowerBudget,
                UpperBudget = enquiry.UpperBudget,
                NoOfBHKs = enquiry.NoOfBhks,
                BHKType = enquiry.BHKType,
                CarpetArea = enquiry.CarpetArea,
                CarpetAreaUnitId = enquiry.CarpetAreaUnitId,
                Currency = enquiry?.Currency,
                BHKs = enquiry?.BHKs,
                EnquiryTypes = enquiry?.EnquiryTypes,
                BHKTypes = enquiry?.BHKTypes,
                BuiltUpArea = enquiry?.BuiltUpArea,
                BuiltUpAreaUnitId = enquiry?.BuiltUpAreaUnitId ?? Guid.Empty,
                SaleableArea = enquiry?.SaleableArea,
                SaleableAreaUnitId = enquiry?.SaleableAreaUnitId ?? Guid.Empty,
                NetAreaUnitId = enquiry?.NetAreaUnitId ?? Guid.Empty,
                PropertyAreaUnitId = enquiry?.PropertyAreaUnitId ?? Guid.Empty,
                NetArea = enquiry?.NetArea,
                PropertyArea = enquiry?.PropertyArea,
                UnitName = enquiry?.UnitName,
                ClusterName = enquiry?.ClusterName,
                PropertyTypes = enquiry?.PropertyTypes,
                OfferType = enquiry?.OfferType,
                Purpose = enquiry?.Purpose,
                Address = new Address()
                {
                    SubLocality = enquiry?.Address?.SubLocality,
                    Locality = enquiry?.Address?.Locality,
                    PlaceId = enquiry?.Address?.PlaceId,
                    City = enquiry?.Address?.City,
                    District = enquiry?.Address?.District,
                    State = enquiry?.Address?.State,
                    Country = enquiry?.Address?.Country,
                    TowerName = enquiry?.Address?.TowerName,
                    Community = enquiry?.Address?.Community,
                    SubCommunity = enquiry?.Address?.SubCommunity,
                },

            };
            if (enquiry?.Addresses?.Any() ?? false)
            {
                var addresses = new List<Address>();
                foreach (var address in enquiry.Addresses)
                {
                    addresses.Add(new Address()
                    {
                        SubLocality = address?.SubLocality,
                        Locality = address?.Locality,
                        PlaceId = address?.PlaceId,
                        City = address?.City,
                        District = address?.District,
                        State = address?.State,
                        Country = address?.Country,
                        TowerName = address?.TowerName,
                        Community = address?.Community,
                        SubCommunity = address?.SubCommunity,
                    });
                }
                leadEnquiry.Addresses = addresses;
            }

            leadEnquiry.LeadSource = source ?? LeadSource.Data;

            if (propertyTypes != null)
            {
                leadEnquiry.PropertyTypes = propertyTypes;
                leadEnquiry.PropertyType = propertyTypes.FirstOrDefault();

            }
            return leadEnquiry;
        }

        #region Set Project 

        protected async Task SetProspectProjectAsync(Lrb.Domain.Entities.Prospect prospect, List<string>? projectList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {

            List<Lrb.Domain.Entities.Project> tempProject = new();
            projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);
            if (projectList?.Any() ?? false)
            {
                foreach (var newProject in projectList)
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByNameSpecs(newProject), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        tempProject.Add(existingProject);
                    }
                    else
                    {
                        Domain.Entities.Project projects = new()
                        {
                            Name = newProject,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        projects = await _projectRepo.AddAsync(projects, cancellationToken);
                        tempProject.Add(projects);
                    }
                }
            }
            prospect.Projects = tempProject;
        }

        #endregion

        #region Set Property
        protected async Task SetProspectPropertiesAsync(Lrb.Domain.Entities.Prospect prospect, List<string>? propertiesList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {
            List<Domain.Entities.Property>? properties = new();
            propertiesList = (propertiesList?.Any() ?? false) ? propertiesList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

            if (propertiesList?.Any() ?? false)
            {
                foreach (var newProperty in propertiesList)
                {
                    var existingProperty = (await _propertyRepo.ListAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken)).FirstOrDefault();
                    if (existingProperty != null)
                    {
                        properties.Add(existingProperty);
                    }
                    else
                    {
                        Domain.Entities.Property property = new()
                        {
                            Title = newProperty,
                            MonetaryInfo = new PropertyMonetaryInfo
                            {
                                Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        property = await _propertyRepo.AddAsync(property, cancellationToken);
                        properties.Add(property);
                    }
                }
            }
            prospect.Properties = properties;
        }
        protected async Task SetProspectAgenciesAsync(Lrb.Domain.Entities.Prospect prospect, List<AgencyDto>? agencies, CancellationToken cancellationToken)
        {
            List<Domain.Entities.Agency>? updatedAgencies = new();
            agencies = (agencies?.Any() ?? false) ? agencies.Where(i => !string.IsNullOrWhiteSpace(i.Name)).ToList() : null;
            if (agencies?.Any() ?? false)
            {
                foreach (var agency in agencies)
                {
                    var existingAgency = (await _agencyRepo.ListAsync(new GetAgencyByNameSpec(agency.Name), cancellationToken)).FirstOrDefault();
                    if (existingAgency != null)
                    {
                        updatedAgencies.Add(existingAgency);
                    }
                    else
                    {
                        Domain.Entities.Agency newAgency = new() { Name = agency.Name };
                        newAgency = await _agencyRepo.AddAsync(newAgency, cancellationToken);
                        updatedAgencies.Add(newAgency);
                    }
                }
            }
            prospect.Agencies = updatedAgencies;
        }
        #endregion

        #region Set ChannelPertners
        protected async Task SetProspectChannelPartnersAsync(Prospect prospect, List<string>? channelPartnersList, CancellationToken cancellationToken)
        {
            List<Lrb.Domain.Entities.ChannelPartner> channelPartners = new();
            channelPartnersList = (channelPartnersList?.Any() ?? false) ? channelPartnersList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            if (channelPartnersList?.Any() ?? false)
            {
                foreach (var newChannelPartner in channelPartnersList)
                {
                    var existingChannelPartner = (await _channelPartnerRepo.ListAsync(new GetChannelPartnerByNameSpecs(newChannelPartner), cancellationToken)).FirstOrDefault();
                    if (existingChannelPartner != null)
                    {
                        channelPartners.Add(existingChannelPartner);
                    }
                    else
                    {
                        Lrb.Domain.Entities.ChannelPartner channelPartner = new() { FirmName = newChannelPartner };
                        channelPartner = await _channelPartnerRepo.AddAsync(channelPartner, cancellationToken);
                        channelPartners.Add(channelPartner);
                    }
                }
            }
            prospect.ChannelPartners = channelPartners;
        }
        #endregion
        #region Set Campaign
        protected async Task SetProspectCampaignsAsync(Prospect prospect, List<CampaignDto>? campaigns, CancellationToken cancellationToken)
        {

            List<Domain.Entities.Campaign>? updatedCampaigns = new();
            campaigns = (campaigns?.Any() ?? false) ? campaigns.Where(i => !string.IsNullOrWhiteSpace(i.Name)).ToList() : null;
            if (campaigns?.Any() ?? false)
            {
                foreach (var campaign in campaigns)
                {
                    var existingCampaign = (await _campaignRepo.ListAsync(new GetCampaignByNameSpec(campaign.Name), cancellationToken)).FirstOrDefault();
                    if (existingCampaign != null)
                    {
                        updatedCampaigns.Add(existingCampaign);
                    }
                    else
                    {
                        Domain.Entities.Campaign newCampaign = new() { Name = campaign.Name };
                        newCampaign = await _campaignRepo.AddAsync(newCampaign, cancellationToken);
                        updatedCampaigns.Add(newCampaign);
                    }
                }
            }
            prospect.Campaigns = updatedCampaigns;
        }
        #endregion
        protected async Task<(List<Domain.Entities.Prospect> Prospects, List<ProspectHistory> Histories)> ArchiveProspectsAsync(List<Guid> prospectIds, CancellationToken cancellationToken)
        {
            try
            {
                (List<Domain.Entities.Prospect> Prospects, List<ProspectHistory> Histories) prospectData = new() { Prospects = new(), Histories = new() };
                var currentUserId = _currentUser.GetUserId();

                var prospects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(prospectIds), cancellationToken);
                var statuses = await _prospectStatusRepo.ListAsync();
                var propertyTypes = await _propertyTypeRepo.ListAsync();
                var sources = await _prospectSourceRepo.ListAsync();
                var userIds = new List<string?>
                {
                    prospects.Select(i=>i.AssignTo).ToList().ToString(),
                    prospects.Select(i=>i.LastModifiedBy).ToList().ToString(),
                    prospects.Select(i=>i.AssignedFrom).ToList().ToString(),
                    prospects.Select(i=>i.SourcingManager).ToList().ToString(),
                    prospects.Select(i=>i.ClosingManager).ToList().ToString(),
                };
                var userDetails = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);

                if (prospects?.Any() ?? false)
                {
                    foreach (var prospect in prospects)
                    {
                        var oldProspect = prospect?.Adapt<ViewProspectDto>();
                        prospect.IsArchived = true;
                        prospect.ArchivedOn = DateTime.UtcNow;
                        prospect.ArchivedBy = _currentUser.GetUserId();

                        var prospectVM = prospect.Adapt<ViewProspectDto>();
                        prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails, cancellationToken);
                        oldProspect = await ProspectHistoryHelper.SetUserViewForProspectV1(oldProspect, userDetails, cancellationToken);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);

                        prospectData.Prospects.Add(prospect);
                        if (histories != null)
                        {
                            prospectData.Histories.AddRange(histories);
                        }
                    }
                    return prospectData;
                }
                else
                {
                    throw new NotFoundException("No leads found by the provided ids.");
                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }

    }
}
