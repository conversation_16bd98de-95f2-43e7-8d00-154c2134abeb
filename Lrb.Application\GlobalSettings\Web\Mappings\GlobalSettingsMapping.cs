﻿using Newtonsoft.Json;

namespace Lrb.Application.GlobalSettings.Web
{
    public static class GlobalSettingsMapping
    {
        public static void Configure()
        {
            TypeAdapterConfig<Domain.Entities.GlobalSettings, ViewGlobalSettingsDto>
                .NewConfig()
                .Map(dest => dest.NotificationSettings, src => src.NotificationSettings != null ? JsonConvert.DeserializeObject<NotificationSettings>(src.NotificationSettings) : null)
                .Map(dest => dest.CallSettings, src => src.CallSettings != null ? JsonConvert.DeserializeObject<CallSettings>(src.CallSettings) : null)
                .Map(dest => dest.LeadNotesSetting, src => src.LeadNotesSetting != null ? JsonConvert.DeserializeObject<LeadNotesSetting>(src.LeadNotesSetting) : null)
                .Map(dest => dest.HasInternationalSupport, src => src.HasInternationalSupport)
                .Map(dest => dest.LeadPropertySetting, src => src.LeadPropertySetting != null ? JsonConvert.DeserializeObject<LeadPropertySetting>(src.LeadPropertySetting) : null)
                .Map(dest => dest.LeadProjectSetting, src => src.LeadProjectSetting != null ? JsonConvert.DeserializeObject<LeadProjectSetting>(src.LeadProjectSetting) : null)
                .Map(dest => dest.OTPSettings, src => src.OTPSettings != null ? JsonConvert.DeserializeObject<OTPSettings>(src.OTPSettings) : null)
                .Map(dest => dest.ShouldRenameSiteVisitColumn, src => src.ShouldRenameSiteVisitColumn != null ? src.ShouldRenameSiteVisitColumn : false)
                .Map(dest => dest.GeneralSettings, src => src.GeneralSettings != null ? JsonConvert.DeserializeObject<GeneralSettings>(src.GeneralSettings) : null)
                .Map(dest => dest.Countries, src => src.CountriesInfo != null ? JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(src.CountriesInfo) : null);

        }
    }
}
