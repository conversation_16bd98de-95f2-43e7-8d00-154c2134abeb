﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAd;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Text.RegularExpressions;

namespace Lrb.Application.Integration.Web.Helpers
{
    public static class GoogleAdLeadHelper
    {
        public static Domain.Entities.Lead? MapToLead(this GoogleAdWebhookDto gaDto)
        {
            Lrb.Domain.Entities.Lead? lead = new();
            lead.Enquiries = new List<LeadEnquiry>();
            LeadEnquiry leadEnquiry = new();
            //leadEnquiry.Address = new();
            var address = new Address();
            lead.Notes = string.IsNullOrEmpty(lead.Notes) ? string.Empty : lead.Notes;
            if (gaDto.user_column_data?.Any() ?? false)
            {
                foreach (var columnData in gaDto.user_column_data)
                {
                    switch (columnData.column_id)
                    {
                        case nameof(UserColumnDataGoogleAd.FIRST_NAME):
                            lead.Name = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.LAST_NAME):
                            lead.Name += columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.FULL_NAME):
                            if (lead.Name == null || lead.Name == "Unknown")
                            {
                                lead.Name = string.IsNullOrWhiteSpace(columnData.string_value) ? "Unknown" : columnData.string_value;
                            }
                            break;
                        case nameof(UserColumnDataGoogleAd.PHONE_NUMBER):
                            lead.ContactNo = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.WORK_PHONE):
                            lead.AlternateContactNo = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.EMAIL):
                            lead.Email = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.WORK_EMAIL):
                            break;
                        case nameof(UserColumnDataGoogleAd.CITY):
                            address.City = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.COUNTRY):
                            address.Country = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.STREET_ADDRESS):
                            address.Locality = columnData.string_value;
                            break;
                        case nameof(UserColumnDataGoogleAd.POSTAL_CODE):
                            address.PostalCode = columnData.string_value;
                            break;
                        default:
                            lead.Notes += columnData.column_id + " - " + columnData.string_value + ";\n";
                            break;
                    }
                }
                if (!string.IsNullOrEmpty(address.City) || !string.IsNullOrEmpty(address.Country) || !string.IsNullOrEmpty(address.Locality))
                {
                    leadEnquiry.Addresses = new List<Address>() { address };
                }
                leadEnquiry.LeadSource = LeadSource.GoogleAds;
                leadEnquiry.IsPrimary = true;
                lead.Enquiries.Add(leadEnquiry);
            }
            return lead;
        }
        public static List<Domain.Entities.Lead> MapToGenericLeads(this IEnumerable<GoogleAdsLeadDto?>? googleLeadDtos, GoogleAdsInfo? ad = null, FacebookLeadGenForm? form = null, bool isInstagramSourceEnabled = false, Domain.Entities.GlobalSettings globalSettings = null)
        {
            const string _name = "name";
            const string _email = "email";
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            const string _city = "city";

            List<Domain.Entities.Lead> leads = new();
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

            if (googleLeadDtos != null)
            {
                foreach (var googleLeadDto in googleLeadDtos)
                {
                    Domain.Entities.Lead lead = new();
                    var fieldData = googleLeadDto?.FieldData;
                    if (fieldData != null)
                    {
                        var contactNo = string.Empty;
                        try
                        {
                            var input = ExtractFacebookContactNumber(fieldData);
                            var contactnumberTask = ValidateContactNumbers(input ?? string.Empty, globalSettings, ad?.CountryCode ?? googleLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91");
                            var contactnumber = contactnumberTask.GetAwaiter().GetResult();
                            if (string.IsNullOrEmpty(input))
                            {
                                input = fieldData?.FirstOrDefault(i => !(i.Name?.ToLower()?.Contains("budget") ?? false) && !(i.Name?.ToLower()?.Contains("cost") ?? false) && !(i.Name?.ToLower()?.Contains("price") ?? false) && i.Values != null && i.Values.Any(j => Regex.Match(j ?? string.Empty, @"[0-9]+$").Value.Length >= 10))?.Values?[0];
                                //input = fieldData?.FirstOrDefault(i => i.Values?.Any(i => (Regex.Replace(i, @"[\(\) -]", "")?.Length > 10) && (Regex.Replace(i, @"[\(\) -]", "")?.Substring(input.Length - 10)?.All(char.IsDigit) ?? false)) ?? false).values?.FirstOrDefault();
                            }
                            if (!string.IsNullOrWhiteSpace(input))
                            {
                                lead.ContactNo = contactnumber;
                                lead.CountryCode = ad?.CountryCode ?? googleLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";

                            }
                            else
                            {
                                lead.ContactNo = string.Empty;
                            }
                        }
                        catch { }
                        if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                        {
                            try
                            {
                                lead.AlternateContactNo = fieldData?.FirstOrDefault(i => i.Name?.ToLowerInvariant() == "work_phone_number")?.Values?.FirstOrDefault();
                                if (!string.IsNullOrEmpty(lead.AlternateContactNo))
                                {
                                    lead.AlternateContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(null, lead.AlternateContactNo, globalSettings, ad?.CountryCode ?? googleLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91");
                                    if (lead.ContactNo == lead.AlternateContactNo)
                                    {
                                        lead.AlternateContactNo = null;
                                        lead.AltCountryCode = googleLeadDto?.CountryCode;

                                    }
                                }
                            }
                            catch { }
                            try
                            {
                                lead.CreatedOnPortal = googleLeadDto?.CreatedTime.ConvertAndSetKindAsUtc();
                            }
                            catch { }
                            try
                            {
                                //lead.Name = string.IsNullOrWhiteSpace((fieldData ?? new()).GetLeadName()) ? "Facebook Enquiry" : (fieldData ?? new()).GetLeadName();
                            }
                            catch { }

                            try
                            {
                                lead.Email = fieldData?.FirstOrDefault(i => i.Name == _email || (i.Name?.ToLower()?.Contains(_email) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                            }
                            catch { }

                            //lead.Notes = JsonConvert.SerializeObject(fieldData, Formatting.Indented);

                            try
                            {
                                List<string> fieldsToAvoid = new()
                        {
                            _name, _email, _phone, _leadPhone
                        };
                                if (fieldData?.Any() ?? false)
                                {
                                    lead.Notes = string.Join(", \n", fieldData.Select(i => !string.IsNullOrEmpty(i.Name) && !fieldsToAvoid.Contains(i.Name) ? i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()) : null).Where(i => i != null));
                                    //.Select(i => i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()));
                                    //lead.Notes = string.Join(" ",additionalFields);
                                }
                            }
                            catch { }
                            if (ad != null)
                            {
                                lead.ConfidentialNotes += Environment.NewLine + $"Ad Name - {ad.AdName},\n Ad Set Name - {ad.AdSetName},\n campaign name - {ad.CampaignName},\n Ad Accountname {ad.AdAccountName}";
                            }
                            if (form != null)
                            {
                                lead.ConfidentialNotes += Environment.NewLine + $"Form Name - {form.Name},\n Form Id - {form?.FacebookId}";
                            }

                            var address = new Address();
                            address.City = fieldData?.FirstOrDefault(i => i.Name == _city || (i.Name?.ToLower()?.Contains(_city) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                            LeadEnquiry enquiry = new();

                            if (!string.IsNullOrWhiteSpace(address.City))
                            {
                                enquiry.Addresses = new List<Address> { address };
                            }
                            enquiry.LeadSource = LeadSource.Facebook;
                            if (isInstagramSourceEnabled)
                            {
                                enquiry.LeadSource = googleLeadDto?.Platform == "ig" ? LeadSource.Instagram : LeadSource.Facebook;
                            }
                            enquiry.SubSource = ad != null ? $"{ad.AdName} (XX{ad.AdId?[^3..]})" : form != null ? $"{form?.Name} (XX{form?.FacebookId?[^3..]})" : string.Empty;
                            enquiry.IsPrimary = true;
                            lead.Enquiries = new List<LeadEnquiry>
                {
                    enquiry
                };
                            leads.Add(lead);
                        }
                    }
                }
            }
            return leads.Where(i => !string.IsNullOrWhiteSpace(i.ContactNo)).ToList();
        }
        private static async Task<string> ValidateContactNumbers(string contactNos, Domain.Entities.GlobalSettings globalSetting, string countryCodes)
        {
            string contactNo = Regex.Replace(contactNos, "[^0-9]", "");
            string defaultRegion = string.Empty;
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSetting?.CountriesInfo);
            var countryCode = countryCodes ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

            if (contactNos.StartsWith("0") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                contactNo = "+91" + contactNo.Substring(1);
                return contactNo;
            }
            if (contactNos.StartsWith("+"))
            {
                PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                defaultRegion = phoneUtil.GetRegionCodeForNumber(number);

            }
            if (string.IsNullOrWhiteSpace(defaultRegion))
            {
                string Code = Regex.Replace(countryCodes, "[^0-9]", "");
                int country = int.Parse(Code);
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(country, new List<string>());
                defaultRegion = regionCodes.FirstOrDefault();
                if (string.IsNullOrWhiteSpace(defaultRegion))
                {
                    defaultRegion = countries?.FirstOrDefault()?.Code;
                }
            }

            PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
            bool isValid;
            if (defaultRegion == "AE")
            {
                if (contactWithCountryCode.Length == 12)
                {
                    return contactWithCountryCode;

                }
            }
            if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
            {
                return contactWithCountryCode;
            }
            else if (contactNos.StartsWith("+") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                return "+" + contactNo;
            }
            else if (contactNos.StartsWith("0") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                return contactNo;
            }
            else if ((string.IsNullOrWhiteSpace(countryCodes) && contactNos.Length > 6 && contactNos.Length < 20))
            {
                return contactNo;
            }

            else if (contactNos.Length > 6 && contactNos.Length < 20)
            {
                return countryCode + contactNo;

            }
            else
            {
                return string.Empty;
            }
        }
        private static string ExtractFacebookContactNumber(List<LeadFormData>? fieldData)
        {
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            string input = string.Empty;
            if (fieldData == null) { return input; }
            input = fieldData.FirstOrDefault(i => i.Name == _phone)?.Values?.FirstOrDefault() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => i.Name == _leadPhone)?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("phone") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("mob") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("contact") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("call") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            return input;
        }
    }
}
