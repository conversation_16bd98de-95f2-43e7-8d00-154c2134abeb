﻿using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using OfficeOpenXml.ConditionalFormatting;
using PhoneNumbers;
using System.Text.RegularExpressions;

namespace Lrb.Application.Integration.Web.Helpers
{
    public static class ListingSitesHelper
    {
        public static DateTime? GetUtcSubmittedDateTime(string? submittedDate, string? submittedTime)
        {
            try
            {
                if (submittedDate == null)
                {
                    return null;
                }
                else
                {
                    var dateTimeString = $"{submittedDate} {submittedTime ?? "00:00:00"}";
                    var dateTime = DateTime.Parse(dateTimeString);
                    return dateTime.ConvertAndSetKindAsUtc();
                }
            }
            catch
            {
                return null;
            }
        }
        public static string ConcatenatePhoneNumber(string? countryCode, string? mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber))
            {
                return string.Empty;
            }
            // Remove any non-numeric characters from the mobile number
            mobileNumber = new string(mobileNumber.Where(char.IsDigit).ToArray());

            // If the mobile number already contains the country code, use it as is
            if (mobileNumber.StartsWith(countryCode ?? string.Empty ) && mobileNumber.Length > 10)
            {
                if (!mobileNumber.StartsWith("+"))
                {
                    mobileNumber = "+" + mobileNumber;
                }
                return mobileNumber;
            }
            // Check if countryCode is null or empty
            if (string.IsNullOrWhiteSpace(countryCode))
            {
                return mobileNumber;
            }

            // If the country code doesn't start with '+', add it
            if (!countryCode.StartsWith("+"))
            {
                countryCode = "+" + countryCode;
            }

            //if country code exist and number start with 0
            if (!string.IsNullOrEmpty(countryCode) && mobileNumber.StartsWith("0"))
            {
                mobileNumber = mobileNumber.Substring(1);
            }

            // Concatenate the country code and mobile number
            return countryCode + mobileNumber;
        }

        public static string ConcatenatePhoneNumberV2(string? countryCodes, string? mobileNumber, Domain.Entities.GlobalSettings globlasettings, string intAccCountryCode)
        {
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globlasettings?.CountriesInfo);

            string country = countryCodes ?? intAccCountryCode;
            int countryCode = 0;
            if (string.IsNullOrWhiteSpace(country))
            {

                country = countries?.FirstOrDefault()?.DefaultCallingCode;
            }
            try
            {
                countryCode = Convert.ToInt32(country);
            }
            catch
            {
                country = countries?.FirstOrDefault()?.DefaultCallingCode;
                countryCode = Convert.ToInt32(country);
            }

            mobileNumber = ValidateContactNumbers(mobileNumber, globlasettings, countryCode, countryCodes);
            if (string.IsNullOrWhiteSpace(mobileNumber))
            {
                return string.Empty;
            }
            return mobileNumber;
        }

        private static string? ValidateContactNumbers(string? conactnum, Domain.Entities.GlobalSettings globalsettings, int countrycodes, string code)
        {
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalsettings?.CountriesInfo);

            string defaultRegion = null;
            string countrycode = countries?.FirstOrDefault()?.DefaultCallingCode;

            string mobileNumber = Regex.Replace(conactnum, "[^0-9]", "");
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            if (conactnum.StartsWith("0") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                mobileNumber = "+91" + mobileNumber.Substring(1);
                return mobileNumber;
            }
            if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                PhoneNumber number = phoneUtil.Parse("+" + mobileNumber, null);
                defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
            }
            if (string.IsNullOrWhiteSpace(defaultRegion))
            {
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countrycodes, new List<string>());
                defaultRegion = regionCodes.FirstOrDefault();
                if (string.IsNullOrWhiteSpace(defaultRegion))
                {
                    defaultRegion = countries?.FirstOrDefault()?.Code;
                    countrycode = countries?.FirstOrDefault()?.DefaultCallingCode;
                }
            }

            PhoneNumber phoneNumber = phoneUtil.Parse(mobileNumber, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
            if (defaultRegion == "AE")
            {
                if (contactWithCountryCode.Length == 12)
                {
                    return contactWithCountryCode;

                }
            }

            bool isValid;
            if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
            {
                return contactWithCountryCode;

            }
            else if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                return "+" + mobileNumber;
            }
            else if (string.IsNullOrWhiteSpace(code) && mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                return mobileNumber;
            }
            else if (mobileNumber.Length > 6 && mobileNumber.Length < 20)
            {
                return code + mobileNumber;
            }
            else
            {
                return string.Empty;
            }
        }
        public static DateTime? GetUtcDateTime(string? date, string? time)
        {
            try
            {
                if (date == null)
                {
                    return null;
                }
                else
                {
                    string dateTimePattern = @"(\d{2}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})";
                    Match match = Regex.Match(date, dateTimePattern);
                    if (match.Success)
                    {
                        var dateTimeString = date;
                        string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                        if (DateTime.TryParseExact(dateTimeString, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                        {
                            return dateTime.ConvertAndSetKindAsUtc();
                        }
                    }
                    else
                    {
                        var dateTimeString = $"{date} {time ?? "00:00:00"}";
                        string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                        if (DateTime.TryParseExact(dateTimeString, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                        {
                            return dateTime.ConvertAndSetKindAsUtc();
                        }
                    }
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }
        public static async Task<(EnquiryType? EnquiredFor, PropertyTypeInfo PropertyTypeInfo)> GetAdditionalFieldsAsync(IDictionary<string, string> additionalProperties, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo, string property, string propertyType)
        {
            try
            {
                EnquiryType? enquiredFor = null;
                PropertyTypeInfo propertyTypeInfo = null;
                var masterPropertyType = await masterPropertRepo.ListAsync();
                if (additionalProperties?.Any() ?? false)
                {
                    if (additionalProperties.ContainsKey("EnquiredFor"))
                    {
                        var enquiry = additionalProperties["EnquiredFor"];
                        if (Enum.TryParse(enquiry, out EnquiryType enquiryType))
                        {
                            enquiredFor = enquiryType;
                        }
                    }
                    if (additionalProperties.ContainsKey("BHKType") && additionalProperties.ContainsKey("NoOfBHK") && !string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        var bhk = additionalProperties["BHKType"];
                        var noOfBhk = additionalProperties["NoOfBHK"];
                        var subProperty = propertyType;
                        var baseProperty = property;
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterPropertyType, bhk, noOfBhk);
                    }
                    else if (additionalProperties.ContainsKey("NoOfBHK"))
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = additionalProperties["NoOfBHK"];
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = string.Empty;
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                }
                else
                {
                    var bHKType = string.Empty;
                    var noOfBhks = string.Empty;
                    if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                    }
                }
                return (enquiredFor, propertyTypeInfo);
            }
            catch
            {
                return (null, null);
            }
        }
        public static async Task<(List<EnquiryType> EnquiryTypes, PropertyTypeInfo PropertyTypeInfo)> GetAdditionalFieldsAsyncV1(IDictionary<string, string> additionalProperties, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo, string property, string propertyType, LeadSource? leadSource = null)
        {
            try
            {
                List<EnquiryType> enquiryTypes = new List<EnquiryType>();
                PropertyTypeInfo propertyTypeInfo = null;
                var masterPropertyType = await masterPropertRepo.ListAsync();

                if (additionalProperties?.Any() ?? false)
                {
                    if (additionalProperties.ContainsKey("EnquiredFor"))
                    {
                        var enquiries = additionalProperties["EnquiredFor"].Split(',');
                        foreach (var enquiry in enquiries)
                        {
                            if (Enum.TryParse(enquiry.Trim(), true, out EnquiryType enquiryType))
                            {
                                enquiryTypes.Add(enquiryType);
                            }
                            if (enquiry.ToLower() == "s")
                            {
                                enquiryTypes.Add(EnquiryType.Buy);
                            }
                            else if (enquiry.ToLower() == "r" || enquiry.ToLower() == "l")
                            {
                                enquiryTypes.Add(EnquiryType.Rent);
                            }
                        }
                    }
                    if (additionalProperties.ContainsKey("BHKType") && additionalProperties.ContainsKey("NoOfBHK") && (!string.IsNullOrEmpty(property) || !string.IsNullOrEmpty(propertyType)))
                    {
                        var bhkString = additionalProperties["BHKType"].ToString(); // Convert to string for checking
                        var noOfBhk = additionalProperties["NoOfBHK"];
                        var subProperty = propertyType;
                        var baseProperty = property;

                        string bhk = "Others";

                        try
                        {
                            if (Enum.IsDefined(typeof(BHKType), bhkString))
                            {
                                bhk = bhkString; 
                            }
                            else if (int.TryParse(bhkString, out int bhkValue) && Enum.IsDefined(typeof(BHKType), bhkValue))
                            {
                                bhk = Enum.GetName(typeof(BHKType), bhkValue);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("Error while processing BHKType: " + ex.Message);
                        }
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterPropertyType, bhk, noOfBhk, leadSource: leadSource);
                    }
                    else if (additionalProperties.ContainsKey("NoOfBHK"))
                    {
                        if (!string.IsNullOrEmpty(property) || !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = additionalProperties["NoOfBHK"];
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks, leadSource: leadSource);
                        }
                    }
                    else if (additionalProperties.ContainsKey("BHKType"))
                    {
                        if (!string.IsNullOrEmpty(property) || !string.IsNullOrEmpty(propertyType))
                        {
                            var bhkString = additionalProperties["BHKType"].ToString(); 
                            var noOfBhks = string.Empty;

                            string bhk = "Others";
                            try
                            {
                                if (Enum.IsDefined(typeof(BHKType), bhkString))
                                {
                                    bhk = bhkString;
                                }
                                else if (int.TryParse(bhkString, out int bhkValue) && Enum.IsDefined(typeof(BHKType), bhkValue))
                                {
                                    bhk = Enum.GetName(typeof(BHKType), bhkValue);
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine("Error while validating BHKType: " + ex.Message);
                            }

                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bhk, noOfBhks, leadSource: leadSource);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(property) || !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = string.Empty;
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks, leadSource: leadSource);
                        }
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        var bHKType = string.Empty;
                        var noOfBhks = string.Empty;
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks, leadSource: leadSource);
                    }
                }

                return (enquiryTypes, propertyTypeInfo);
            }
            catch (Exception ex)
            {
                // Handle exception appropriately
                return (null, null);
            }
        }


        public static async Task<(EnquiryType? EnquiredFor, PropertyTypeInfo PropertyTypeInfo)> GetAdditionalFieldsAsync(IDictionary<string, string>? additionalProperties, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo, string property, string propertyType, string? unit)
        {
            try
            {
                EnquiryType? enquiredFor = null;
                PropertyTypeInfo propertyTypeInfo = null;
                var masterPropertyType = await masterPropertRepo.ListAsync();
                if (additionalProperties?.Any() ?? false)
                {
                    if (additionalProperties.ContainsKey("EnquiredFor"))
                    {
                        var enquiry = additionalProperties["EnquiredFor"];
                        if (Enum.TryParse(enquiry, out EnquiryType enquiryType))
                        {
                            enquiredFor = enquiryType;
                        }
                    }
                    if (additionalProperties.ContainsKey("BHKType") && additionalProperties.ContainsKey("NoOfBHK") && !string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        var bhk = additionalProperties["BHKType"];
                        var noOfBhk = additionalProperties["NoOfBHK"];
                        var subProperty = propertyType;
                        var baseProperty = property;
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterPropertyType, bhk, noOfBhk);
                    }
                    else if (additionalProperties.ContainsKey("NoOfBHK"))
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = additionalProperties["NoOfBHK"];
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = string.Empty;
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                }
                else
                {
                    var bHKType = string.Empty;
                    var noOfBhks = string.Empty;
                    if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                    }
                }
                return (enquiredFor, propertyTypeInfo);
            }
            catch
            {
                return (null, null);
            }
        }
        public static async Task<(List<EnquiryType> EnquiryTypes, PropertyTypeInfo PropertyTypeInfo)> GetAdditionalFieldsAsyncV1(IDictionary<string, string>? additionalProperties, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo, string property, string propertyType, string? unit)
        {
            try
            {
                List<EnquiryType> enquiryTypes = new();
                PropertyTypeInfo propertyTypeInfo = null;
                var masterPropertyType = await masterPropertRepo.ListAsync();
                if (additionalProperties?.Any() ?? false)
                {
                    if (additionalProperties.ContainsKey("EnquiredFor"))
                    {
                        var enquiries = additionalProperties["EnquiredFor"].Split(',');
                        foreach (var enquiry in enquiries)
                        {
                            if (Enum.TryParse(enquiry.Trim(), true, out EnquiryType enquiryType))
                            {
                                enquiryTypes.Add(enquiryType);
                            }
                        }
                    }
                    if (additionalProperties.ContainsKey("BHKType") && additionalProperties.ContainsKey("NoOfBHK") && !string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        var bhk = additionalProperties["BHKType"];
                        var noOfBhk = additionalProperties["NoOfBHK"];
                        var subProperty = propertyType;
                        var baseProperty = property;
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, masterPropertyType, bhk, noOfBhk);
                    }
                    else if (additionalProperties.ContainsKey("NoOfBHK"))
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = additionalProperties["NoOfBHK"];
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                        {
                            var bHKType = string.Empty;
                            var noOfBhks = string.Empty;
                            propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                        }
                    }
                }
                else
                {
                    var bHKType = string.Empty;
                    var noOfBhks = string.Empty;
                    if (!string.IsNullOrEmpty(property) && !string.IsNullOrEmpty(propertyType))
                    {
                        propertyTypeInfo = BulkUploadHelper.GetPropertyType(property, propertyType, masterPropertyType, bHKType, noOfBhks);
                    }
                }
                return (enquiryTypes, propertyTypeInfo);
            }
            catch
            {
                return (null, null);
            }
        }

        public static async Task<(EnquiryType? EnquiredFor, PropertyTypeInfo? propetyInfo)> CreateEnquiryFromIntegration(string lookingFor, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo)
        {
            try
            {
                var propertyTypes = await masterPropertRepo.ListAsync();
                string subProperty = string.Empty;
                string baseProperty = string.Empty;
                string noOfBhk = string.Empty;
                string bHKTypes = string.Empty;
                string enquiryFor = string.Empty;
                List<string> subProprtyTypes = new() { "flat", "independent house", "villa", "shop", "row villa", "land", "office space", "hostel guest house", "plot", "showroom", "godown", "chambers", "farm house", "basement", "guest house", "kiosk", "complete building", "studio", "farmhouse land", "hotel space", "agricultural land", "industrial space" };
                foreach (var property in subProprtyTypes)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{property}\b"))
                    {
                        subProperty = property;
                    }
                }
                Match match = Regex.Match(lookingFor, @"(\d+)\s*bhk");
                if (match.Success)
                {
                    noOfBhk = match.Groups[1].Value;
                }
                List<string> basePropertyType = new() { "residential", "commercial", "agricultural" };
                foreach (var property in basePropertyType)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{property}\b"))
                    {
                        baseProperty = property;
                    }
                }
                List<string> bhkTypes = new() { "simplex", "duplex", "pentHouse", "others" };
                foreach (var bhk in bhkTypes)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{bhk}\b"))
                    {
                        bHKTypes = bhk;
                    }
                }
                List<string> enquiryType = new() { "Buy", "Sale", "Rent" };
                foreach (var enquiry in enquiryType)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{enquiry}\b"))
                    {
                        enquiryFor = enquiry;
                    }
                }
                EnquiryType? enquiredFor = null;
                if (!string.IsNullOrEmpty(enquiryFor))
                {
                    if (Enum.TryParse(enquiryFor, out EnquiryType type))
                    {
                        enquiredFor = type;
                    }
                }
                var propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, propertyTypes, bHKTypes, noOfBhk);
                return (enquiredFor, propertyTypeInfo);
            }
            catch
            {
                return (null, null);
            }
        }
        public static async Task<(List<EnquiryType>? EnquiryTypes, PropertyTypeInfo? propetyInfo)> CreateEnquiryFromIntegrationV1(string lookingFor, IRepositoryWithEvents<MasterPropertyType> masterPropertRepo)
        {
            try
            {
                var propertyTypes = await masterPropertRepo.ListAsync();
                string subProperty = string.Empty;
                string baseProperty = string.Empty;
                string noOfBhk = string.Empty;
                string bHKTypes = string.Empty;
                string enquiryFor = string.Empty;
                List<string> subProprtyTypes = new() { "flat", "independent house", "villa", "shop", "row villa", "land", "office space", "hostel guest house", "plot", "showroom", "godown", "chambers", "farm house", "basement", "guest house", "kiosk", "complete building", "studio", "farmhouse land", "hotel space", "agricultural land", "industrial space" };
                foreach (var property in subProprtyTypes)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{property}\b"))
                    {
                        subProperty = property;
                    }
                }
                Match match = Regex.Match(lookingFor, @"(\d+)\s*bhk");
                if (match.Success)
                {
                    noOfBhk = match.Groups[1].Value;
                }
                List<string> basePropertyType = new() { "residential", "commercial", "agricultural" };
                foreach (var property in basePropertyType)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{property}\b"))
                    {
                        baseProperty = property;
                    }
                }
                List<string> bhkTypes = new() { "simplex", "duplex", "pentHouse", "others" };
                foreach (var bhk in bhkTypes)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{bhk}\b"))
                    {
                        bHKTypes = bhk;
                    }
                }
                List<EnquiryType> enquiryTypes = new();
                List<string> enquiryType = new() { "Buy", "Sale", "Rent" };
                foreach (var enquiry in enquiryType)
                {
                    if (Regex.IsMatch(lookingFor, $@"\b{enquiry}\b"))
                    {
                        if (Enum.TryParse(enquiry, out EnquiryType type))
                        {
                            enquiryTypes.Add(type);
                        }
                    }
                }
                var propertyTypeInfo = BulkUploadHelper.GetPropertyType(baseProperty, subProperty, propertyTypes, bHKTypes, noOfBhk);
                return (enquiryTypes, propertyTypeInfo);
            }
            catch
            {
                return (null, null);
            }
        }

        public static async Task<bool> CreateLeadAssignmentHistory(Domain.Entities.Lead lead, IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo, CancellationToken cancellationToken)
        {
            try
            {
                var assignment = new LeadAssignment()
                {
                    AssignTo = lead.AssignTo,
                    AssignedFrom = lead.AssignedFrom,
                    Notes = lead.Notes,
                    LeadId = lead.Id,
                    UserId = lead.AssignTo,
                    LeadAssignmentType = LeadAssignmentType.WithHistory,
                    AssignmentDate = DateTime.UtcNow,
                };
                if (lead.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignTo = lead.SecondaryUserId;
                }
                if (lead.SecondaryFromUserId != null && lead.SecondaryFromUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                }

                // Set OriginalOwner to the assigned user when first assigned
                if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                {
                    lead.OriginalOwner = lead.AssignTo;
                }

                await leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                //ignore
                return false;
            }
        }

    }
}
