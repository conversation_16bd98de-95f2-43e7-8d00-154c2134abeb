﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests
{
    public class ProcessFacebookWebhookRequest : IRequest<Response<bool>>
    {
        public FacebookPageWebhookDto Dto { get; set; } = default!;
        public string TenantId { get; set; } = default!;
        public int RotationCounter { get; set; } = 0;
    }
    public class ProcessFacebookWebhookRequestHandler : FBCommonHandler, IRequestHandler<ProcessFacebookWebhookRequest, Response<bool>>
    {
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInfoRepo;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<FacebookLeadInfo> _fbLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IMediator _mediator;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;

        public ProcessFacebookWebhookRequestHandler(
            IFacebookService facebookService,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            ILogger logger,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInfoRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsInfoRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            ICurrentUser currentUser,
            INotificationSenderService notificationSenderServiceRepo,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            IJobService hangfireService,
            ITenantIndependentRepository repository,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,

            IRepositoryWithEvents<FacebookLeadInfo> fbLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<Address> addressRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IMediator mediator,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService,
            IGoogleAdsService googleAdsService,
            IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
            IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
            : base(facebookAuthResponseRepo,
                facebookConnectedPageAccountRepo,
                facebookLeadGenFormRepo,
                facebookService,
                hangfireService,
                repository,
                currentUser,
                integrationAccInfoRepo,
                fbAdsInfoRepo,
                leadRepositoryAsync,
                googleAdsService,
                googleAdsAuthResponseRepo,
                googleAdsRepo,
                googleCampaignsRepo)
        {
            //_leadStatusRepo = leadStatusRepo;
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _logger = logger;
            _integrationAssignmentInfoRepo = integrationAssignmentInfoRepo;
            _fbAdsInfoRepo = fbAdsInfoRepo;
            _projectRepo = projectRepo;
            _notificationSenderService = notificationSenderServiceRepo;
            _npgsqlRepo = npgsqlRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _fbLeadInfoRepo = fbLeadInfoRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _addressRepo = addressRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _mediator = mediator;
            _leadRotationService = leadRotationService;
            _leadEnquiryRepo = leadEnquiryRepo;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
        }

        //New Implementation

        public async Task<FacebookAdsInfo?> GetAdInfoAsync(string adId, string token, Guid accountId, string pageId)
        {
            try
            {
                var apiUrl = $"https://graph.facebook.com/v17.0/{adId}";
                var parms = "fields=id,name,effective_status,adset_id,adset{id,name},account_id,campaign_id,campaign{id,name}";
                var accessToken = "access_token=" + token;
                var url = apiUrl + "?" + parms + "&" + accessToken;
                RestClient client = new(url);
                RestRequest reque1st = new();
                reque1st.Method = Method.Get;
                RestResponse? res = null;
                try
                {
                    res = await client.GetAsync(reque1st);
                }
                catch (Exception e)
                {
                    _logger.Information($"FacebookService -> GetAllFacebookAdsAsync() Error: {e?.InnerException?.Message ?? e?.Message}");
                }
                if (res.StatusCode != System.Net.HttpStatusCode.OK)
                {
                }
                var adsResponse = JsonConvert.DeserializeObject<FacebookV2AdDto>(res?.Content ?? string.Empty);
                if (adsResponse != null)
                {
                    var accountInfo = await GetAccountInfoAsync(token, adsResponse.account_id);
                    var addAccount = new FacebookAdsInfo()
                    {
                        AdId = adsResponse?.id,
                        AdName = adsResponse?.name,
                        AdAccountId = accountInfo?.id,
                        AdAccountName = accountInfo?.name,
                        AdSetId = adsResponse?.adset_id,
                        AdSetName = adsResponse?.adset?.name,
                        CampaignId = adsResponse?.campaign_id,
                        CampaignName = adsResponse?.campaign?.name,
                        Status = adsResponse?.effective_status,
                        FacebookAuthResponseId = accountId,
                        IsSubscribed = true,
                        PageId = pageId
                    };
                    var account = await _fbAdsInfoRepo.AddAsync(addAccount);
                    return account;
                }
                return null;
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public async Task<FacebookAdAccountDto?> GetAccountInfoAsync(string token, string accountId)
        {
            try
            {
                var apiUrl = $"https://graph.facebook.com/v17.0/act_{accountId}";
                var parms = "fields=name";
                var accessToken = "access_token=" + token;
                var url = apiUrl + "?" + parms + "&" + accessToken;
                RestClient client = new(url);
                RestRequest reque1st = new();
                reque1st.Method = Method.Get;
                RestResponse? res = null;
                try
                {
                    res = await client.GetAsync(reque1st);
                }
                catch (Exception e)
                {
                    _logger.Information($"FacebookService -> GetAllFacebookAdsAsync() Error: {e?.InnerException?.Message ?? e?.Message}");
                }
                var adsResponse = JsonConvert.DeserializeObject<FacebookAdAccountDto>(res.Content);
                return adsResponse;
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public async Task<Response<bool>> Handle(ProcessFacebookWebhookRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.RotationCounter < 2)
                {
                    _logger.Information($"**********************************  Started Facebook - {request.TenantId} ************************************");
                    Console.WriteLine($"**********************************  Started Facebook - {request.TenantId} ************************************");

                    try
                    {
                        Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                        var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

                        _logger.Information($"Started processing facebook webhook request for Tenant: {request.TenantId}, Dto: " + JsonConvert.SerializeObject(request.Dto));
                        Console.WriteLine($"Started processing facebook webhook request for Tenant: {request.TenantId}, Dto: " + JsonConvert.SerializeObject(request.Dto));
                        var leadgenInfo = request.Dto?.entry?.FirstOrDefault()?.changes?.FirstOrDefault();
                        _logger.Information("ProcessFacebookWebhookRequestHandler -> LeadgenInfo: " + JsonConvert.SerializeObject(leadgenInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        Console.WriteLine("ProcessFacebookWebhookRequestHandler -> LeadgenInfo: " + JsonConvert.SerializeObject(leadgenInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        var formId = leadgenInfo?.value?.form_id ?? string.Empty;
                        var leadgenId = leadgenInfo?.value?.leadgen_id ?? string.Empty;
                        var pageId = leadgenInfo?.value?.page_id ?? string.Empty;
                        var adId = leadgenInfo?.value?.ad_id ?? string.Empty;
                        var createdTime = leadgenInfo?.value?.created_time ?? 0;
                        var ad = await _fbAdsInfoRepo.FirstOrDefaultAsync(new FacebookAdsByAdIdSpec(adId), cancellationToken);
                        var formData = await _facebookLeadGenFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByFormIdSpec(formId), cancellationToken);
                        var fbPageAccount = await _facebookConnectedPageAccountRepo.FirstOrDefaultAsync(new GetFacebookConnectedPageAccountByFBIdSpec(pageId)) ?? throw new Exception("No Facebook account found by the page id.");
                        if (ad == null)
                        {
                            try
                            {
                                ad = await GetAdInfoAsync(adId, fbPageAccount.AccessToken, fbPageAccount?.FacebookAuthResponse?.Id ?? Guid.Empty, pageId);
                            }
                            catch (Exception e) { }
                        }
                        ////Todo: searched for deleted pages.
                        //var fbPageAccount = await _dapperRepository.GetFacebookPageAccountAsync(request.TenantId, pageId) ?? throw new Exception("No Facebook page account found by the page id.");
                        //var fbAuthResponse = await _facebookAuthResponseRepo.FirstOrDefaultAsync(new GetFacebookAuthResponseByIdSpec(fbPageAccount.FacebookAuthResponseId), cancellationToken) ?? throw new Exception("No Facebook page account found by the page id.");

                        await _npgsqlRepo.RestoreFBPageAsync(pageId, request.TenantId);
                        var fbAuthResponse = fbPageAccount.FacebookAuthResponse;
                        var integrationAccountInfo = await _integrationAccInfoRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(fbPageAccount.FacebookAuthResponseId), cancellationToken);
                        if ((ad != null && ad.IsSubscribed) || (formData != null && formData.IsSubscribed))
                        {
                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Stored Form Data: " + JsonConvert.SerializeObject(formData, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Stored Form Data: " + JsonConvert.SerializeObject(formData, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));

                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Stored Ad Data: " + JsonConvert.SerializeObject(ad, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Stored Ad Data: " + JsonConvert.SerializeObject(ad, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            try
                            {
                                if (integrationAccountInfo == null)
                                {
                                    integrationAccountInfo = new IntegrationAccountInfo()
                                    {
                                        Id = Guid.NewGuid(),
                                        AccountName = fbPageAccount.FacebookAuthResponse?.FacebookAccountName,
                                        LeadSource = LeadSource.Facebook,
                                        LicenseId = Guid.NewGuid(),
                                        JsonTemplate = "",
                                        FacebookAccountId = fbPageAccount.FacebookAuthResponse?.Id ?? Guid.Empty,
                                    };
                                    await _integrationAccInfoRepo.AddAsync(integrationAccountInfo);
                                }
                                _logger.Information("ProcessFacebookWebhookRequestHandler -> Stored AccountData: " + JsonConvert.SerializeObject(fbPageAccount, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Stored AccountData: " + JsonConvert.SerializeObject(fbPageAccount, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                var fbLeadData = await _facebookService.GetLeadInfoAsync(leadgenId, fbPageAccount.LongLivedPageAccessToken);
                                var fbLead = fbLeadData.FBLead;

                                if (fbLeadData.IsActive != fbAuthResponse.IsActive)
                                {
                                    fbAuthResponse.IsActive = fbLeadData.IsActive;
                                    await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse, cancellationToken);
                                }
                                if (fbLead == null)
                                {
                                    //return new(false, "Something went wrong in Your Permissions and Your Facebook Account please Check it and Try again.");
                                    return new(false, "There is a permissions issue or a problem with the Facebook account. Please check and try again.");
                                }
                                fbLead.CountryCode = ad?.CountryCode ?? formData?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";

                                try
                                {
                                    if ((await _fbLeadInfoRepo.GetByIdAsync(fbLead.Id, cancellationToken)) == null)
                                    {
                                        await _fbLeadInfoRepo.AddAsync(fbLead.Adapt<FacebookLeadInfo>(), cancellationToken);
                                    }
                                }
                                catch (Exception e)
                                {

                                }
                                _logger.Information("ProcessFacebookWebhookRequestHandler -> Facebook Lead: " + JsonConvert.SerializeObject(fbLead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Facebook Lead: " + JsonConvert.SerializeObject(fbLead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                var lead = fbLead.MapToGenericLead(ad, formData, globalSettings?.IsInstagramSourceEnabled ?? false, globalSettings, leadgenId, createdTime);

                                #region Facebook Conversion event trigger
                                //try
                                //{
                                //    if (globalSettings.EnableFacebookConversion && (!string.IsNullOrEmpty(lead?.MetaLeadId)))
                                //    {
                                //        lead.PixelId = fbPageAccount.FacebookAuthResponse?.PixelId ?? string.Empty;
                                //        FbConversionApiDto fbConversionApiDto = new();
                                //        fbConversionApiDto.AccessToken = fbAuthResponse.ConversionsAccessToken;
                                //        fbConversionApiDto.MetaLeadIds = new List<string> { lead.MetaLeadId };
                                //        fbConversionApiDto.StatusName = "new";
                                //        await _serviceBus.RunFbConversionApiEventAsync(fbConversionApiDto);
                                //    }
                                //}
                                //catch (Exception ex)
                                //{
                                //    _logger.Information($"IntegrationController -> ProcessFacebookWebhookRequest, Facebook Conversion event trigger:" + JsonConvert.SerializeObject(ex.Message));
                                //}
                                #endregion

                                _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead: " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Mapped Lead: " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));

                                if (lead != null && !string.IsNullOrEmpty(lead.ContactNo))
                                {
                                    List<Domain.Entities.Lead>? duplicateLeads = null;
                                    var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                                    if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                                    {
                                        if (!duplicateFeatureInfo.AllowAllDuplicates)
                                        {
                                            var duplicateLeadSpecDto = lead.Adapt<DuplicateLeadSpecDto>();
                                            try
                                            {
                                                (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(adId: ad?.Id, formId: formData?.Id, intgrAccId: integrationAccountInfo?.Id, fbAdsRepo: _fbAdsRepo, fbFormRepo: _facebookLeadGenFormRepo, integrationAccRepo: _integrationAccInfoRepo);
                                                duplicateLeadSpecDto.ProjectsList = new List<string>() { AssignedProject?.Name ?? string.Empty };
                                            }
                                            catch (Exception ex) { }
                                            duplicateLeadSpecDto.SubSource = lead.Enquiries?.FirstOrDefault()?.SubSource?.ToLower() ?? string.Empty;
                                            duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto), cancellationToken);
                                        }
                                    }
                                    else
                                    {
                                        duplicateLeads ??= new();
                                        var leadContacts = new List<string>() { (lead.ContactNo?.Length >= 1 ? lead.ContactNo : "invalid ContactNo") ?? "invalid ContactNo", (lead.AlternateContactNo?.Length >= 1 ? lead.AlternateContactNo : "invalid ContactNo") ?? "invalid ContactNo" };
                                        var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec(leadContacts), cancellationToken);
                                        if (duplicateLead != null)
                                        {
                                            duplicateLeads.Add(duplicateLead);
                                        }
                                    }
                                    bool? duplicateCheckResponse;
                                    if (duplicateLeads?.Any() ?? false)
                                    {
                                        var duplicateCheckRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                                        duplicateCheckRequest.ApiKey = integrationAccountInfo.ApiKey;
                                        duplicateCheckRequest.IntegrationAccountId = integrationAccountInfo.Id;
                                        duplicateCheckRequest.FbAdId = adId;
                                        duplicateCheckRequest.FbFormId = formId;
                                        duplicateCheckRequest.LeadSource = LeadSource.Facebook;
                                        duplicateCheckRequest.SerializedData = fbLead.Serialize();
                                        duplicateCheckResponse = (await _mediator.Send(duplicateCheckRequest)).Data;
                                    }
                                    if (!duplicateLeads?.Any() ?? true)
                                    {
                                        var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                                        string name = string.IsNullOrWhiteSpace(lead?.Name?.Trim()) ? "UnKnwon" : lead.Name.Trim();
                                        lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                                        lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                                        lead.TagInfo = new();
                                        lead.AgencyName = formData?.AgencyName ?? ad?.AgencyName;
                                        var agencyToAdd = formData?.Agency ?? ad?.Agency;
                                        lead.Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : lead.Agencies;
                                        lead.AccountId = integrationAccountInfo.Id;
                                        //var leads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo[^10..] }));
                                        #region Automation
                                        (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                                        IntegrationAssignment? integrationAssignmentDetails = null;
                                        if (ad != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(ad.Id, LeadSource.Facebook, fbAdsRepo: _fbAdsRepo))
                                        {
                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsInfoRepo);
                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, fbAdsRepo: _fbAdsInfoRepo);
                                        }
                                        else if (formData != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(formData.Id, LeadSource.Facebook, fbFormRepo: _facebookLeadGenFormRepo))
                                        {
                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo);
                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, fbFormRepo: _facebookLeadGenFormRepo);
                                        }
                                        else if (integrationAccountInfo != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(integrationAccountInfo.Id, LeadSource.Facebook, integrationAccRepo: _integrationAccInfoRepo))
                                        {
                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo);
                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, integrationAccRepo: _integrationAccInfoRepo);
                                        }
                                        IntegrationAssignment? integrationAssignment = null;
                                        try
                                        {
                                            integrationAssignment = await IntegrationAssignmentHelper.GetAssignedProjLocAsyncV1(adId: ad?.Id, formId: formData?.Id, intgrAccId: integrationAccountInfo?.Id, fbAdsRepo: _fbAdsRepo, fbFormRepo: _facebookLeadGenFormRepo, integrationAccRepo: _integrationAccInfoRepo);
                                        }
                                        catch
                                        {

                                        }
                                       // (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(adId: ad?.Id, formId: formData?.Id, intgrAccId: integrationAccountInfo?.Id, fbAdsRepo: _fbAdsRepo, fbFormRepo: _facebookLeadGenFormRepo, integrationAccRepo: _integrationAccInfoRepo);
                                        var locationToAdd = integrationAssignmentDetails?.Location ?? integrationAssignment?.Location;
                                        //Add enquired location from assigned locaiton in the integration account.
                                        if (locationToAdd != null)
                                        {
                                            Address? address = null;
                                            var primaryEnquiry = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary);
                                            if (primaryEnquiry != null)
                                            {
                                                var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(locationToAdd.Id), cancellationToken);
                                                if (existingAddress != null)
                                                {
                                                    address = existingAddress;
                                                }
                                                else
                                                {
                                                    address = locationToAdd.MapToAddress();
                                                    address.Location = locationToAdd;
                                                    await _addressRepo.AddAsync(address);
                                                }
                                                if (address != null)
                                                {
                                                    //primaryEnquiry.Address = address;
                                                    primaryEnquiry.Addresses = new List<Address>() { address };
                                                }
                                            }
                                        }

                                        var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                                        UserDetailsDto? assignedUser = null;
                                        if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                                        {
                                            try
                                            {
                                                assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                                            }
                                            catch (Exception ex)
                                            {
                                            }
                                        }

                                        if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                                        {
                                            lead.AssignTo = existingLead.AssignTo;
                                        }
                                        else
                                        {
                                            List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                                            (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                                            if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased)
                                            {
                                                try
                                                {
                                                    if (ad != null && ad?.UserAssignment != null)
                                                    {
                                                        ad.UserAssignment.TotalLeadsCount = (ad?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                                        var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(ad.Adapt<AccountInfoDto>());
                                                        lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                                    }
                                                    else if (formData != null && formData?.UserAssignment != null)
                                                    {
                                                        formData.UserAssignment.TotalLeadsCount = (formData?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                                        var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(formData.Adapt<AccountInfoDto>());
                                                        lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                                    }
                                                    else if (integrationAccountInfo != null && integrationAccountInfo?.UserAssignment != null)
                                                    {
                                                        integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                                        var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                                        lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                                    }
                                                    else
                                                    {
                                                        lead.AssignTo = assignToRes.AssignTo;
                                                    }
                                                }
                                                catch (Exception ex) { }
                                            }
                                            else
                                            {
                                                var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                                                if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                                                {
                                                    bool isAssigned = true;
                                                    while (isAssigned)
                                                    {
                                                        if (ad != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(ad.Id, LeadSource.Facebook, fbAdsRepo: _fbAdsRepo))
                                                        {
                                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsInfoRepo, priority: userAssignmentAndProject.Priority);
                                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, fbAdsRepo: _fbAdsInfoRepo);
                                                        }
                                                        else if (formData != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(formData.Id, LeadSource.Facebook, fbFormRepo: _facebookLeadGenFormRepo))
                                                        {
                                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo, priority: userAssignmentAndProject.Priority);
                                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, fbFormRepo: _facebookLeadGenFormRepo);
                                                        }
                                                        else if (integrationAccountInfo != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(integrationAccountInfo.Id, LeadSource.Facebook, integrationAccRepo: _integrationAccInfoRepo))
                                                        {
                                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo, priority: userAssignmentAndProject.Priority);
                                                            integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, integrationAccRepo: _integrationAccInfoRepo);
                                                        }
                                                        assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                                        if (assignToRes.AssignTo != Guid.Empty)
                                                        {
                                                            isAssigned = false;
                                                        }
                                                        else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                                        {
                                                            userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                                        }
                                                        else
                                                        {
                                                            isAssigned = false;
                                                        }
                                                    }
                                                }
                                                lead.AssignTo = assignToRes.AssignTo;
                                            }
                                            // Set OriginalOwner to the assigned user when first assigned
                                            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                                            {
                                                lead.OriginalOwner = lead.AssignTo;
                                            }
                                            var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumber(countries?.FirstOrDefault()?.DefaultCallingCode, lead.ContactNo);
                                            if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                                            {
                                                (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, mobileWithCode) : (Guid.Empty, false);
                                                lead.SecondaryUserId = secondaryAssignTo.AssignTo;
                                            }
                                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                                        }
                                        #endregion
                                        try
                                        {
                                            var projectToAdd = userAssignmentAndProject.Project ?? integrationAssignment?.Project;
                                            if (lead.Projects != null && projectToAdd != null)
                                            {
                                                lead.Projects.Add(projectToAdd);
                                            }
                                            else if (projectToAdd != null)
                                            {
                                                lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAdd };
                                            }

                                            if (lead.Agencies != null && integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                                            {
                                                lead.Agencies.Add(integrationAssignment?.Agency);
                                            }
                                            else if (integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                                            {
                                                lead.Agencies ??= new List<Lrb.Domain.Entities.Agency>() { integrationAssignment?.Agency };
                                            }

                                            if (lead.Campaigns != null && integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                                            {
                                                lead.Campaigns.Add(integrationAssignment?.Campaign);
                                            }
                                            else if (integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                                            {
                                                lead.Campaigns ??= new List<Lrb.Domain.Entities.Campaign>() { integrationAssignment?.Campaign };
                                            }
                                            if (lead.ChannelPartners != null && integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                                            {
                                                lead.ChannelPartners.Add(integrationAssignment?.ChannelPartner);
                                            }
                                            else if (integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                                            {
                                                lead.ChannelPartners ??= new List<Lrb.Domain.Entities.ChannelPartner>() { integrationAssignment?.ChannelPartner };
                                            }

                                            if (lead.Properties != null && integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false)
                                            {
                                                lead.Properties.Add(integrationAssignment?.Property);
                                            }
                                            else if (integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false && integrationAssignment?.Property?.IsArchived == false)
                                            {
                                                lead.Properties ??= new List<Lrb.Domain.Entities.Property>() { integrationAssignment?.Property };
                                            }
                                        }
                                        catch
                                        {

                                        }
                                        try
                                        {
                                            var existingUser = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                            _logger.Information("Assigned User: " + JsonConvert.SerializeObject(existingUser, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            Console.WriteLine("Assigned User: " + JsonConvert.SerializeObject(existingUser, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                        }
                                        catch { }

                                        #region DuplicateDetails
                                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                                        if (parentLead != null)
                                        {
                                            _logger.Information($"Parent Lead: {parentLead.Name} - {parentLead.ContactNo} <- {parentLead.CreatedOn}");
                                            Console.WriteLine($"Parent Lead: {parentLead.Name} - {parentLead.ContactNo} <- {parentLead.CreatedOn}");
                                            lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                                            parentLead.ChildLeadsCount += 1;
                                            try
                                            {
                                                await _leadRepo.UpdateAsync(parentLead);
                                            }
                                            catch (Exception e)
                                            {

                                            }

                                        }

                                        #endregion
                                        if (lead.AssignTo != Guid.Empty)
                                        {
                                            lead.AssignDate = DateTime.UtcNow;
                                        }
                                        await _leadRepo.AddAsync(lead);
                                        var contactWithCode = ListingSitesHelper.ConcatenatePhoneNumber(countries?.FirstOrDefault()?.DefaultCallingCode, lead.ContactNo);
                                        #region CreateDuplicateLead
                                        var totalLeadsCount = 0;
                                        try
                                        {
                                            if (ad != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(ad.Id, LeadSource.Facebook, fbAdsRepo: _fbAdsRepo))
                                            {
                                                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(ad.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbAdsRepo: _fbAdsInfoRepo);
                                            }
                                            else if (formData != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(formData.Id, LeadSource.Facebook, fbFormRepo: _facebookLeadGenFormRepo))
                                            {
                                                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(formData.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, fbFormRepo: _facebookLeadGenFormRepo);
                                            }
                                            else if (integrationAccountInfo != null && await IntegrationAssignmentHelper.HasValidAssignmentAsync(integrationAccountInfo.Id, LeadSource.Facebook, integrationAccRepo: _integrationAccInfoRepo))
                                            {
                                                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Facebook, _integrationAssignmentRepo, _assignmentModuleRepo, integrationAccRepo: _integrationAccInfoRepo);
                                            }
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? false) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                                            {
                                                var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                                                if (duplicateLeadAssignmentsIds?.Any() ?? false)
                                                {
                                                    if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                                    {
                                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, contactWithCode);
                                                    }
                                                    else
                                                    {
                                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, contactWithCode);
                                                        await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        try
                                        {
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && ((userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDualAssignmentEnabled ?? false)))
                                            {
                                                var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);

                                                if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                                                {
                                                    await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, contactWithCode);
                                                    await _leadRepo.UpdateRangeAsync(replicatedLeads);
                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion
                                        try
                                        {
                                            _logger.Information($"Lead {lead.Name}({lead.ContactNo}) added in Tenant : {request.TenantId}");
                                            Console.WriteLine($"Lead {lead.Name}({lead.ContactNo}) added in Tenant : {request.TenantId}");
                                        }
                                        catch (Exception e)
                                        {
                                        }
                                        if (ad != null)
                                        {
                                            ad.LeadsCount++;
                                            await _fbAdsInfoRepo.UpdateAsync(ad);
                                        }
                                        else if (formData != null)
                                        {
                                            formData.TotalLeadsCount++;
                                            await _facebookLeadGenFormRepo.UpdateAsync(formData);
                                        }
                                        if (integrationAccountInfo != null)
                                        {
                                            integrationAccountInfo.LeadCount++;
                                            integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                                            await _integrationAccInfoRepo.UpdateAsync(integrationAccountInfo);
                                        }
                                        if (fbAuthResponse != null)
                                        {
                                            if (lead.Enquiries?[0]?.LeadSource == LeadSource.Instagram)
                                            {
                                                fbAuthResponse.InstaLeadCount++;
                                            }
                                            else if (lead.Enquiries?[0]?.LeadSource == LeadSource.Facebook)
                                            {
                                                fbAuthResponse.FbLeadCount++;
                                            }
                                            await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse);
                                        }
                                        var leadDto = lead.Adapt<ViewLeadDto>();
                                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, source: LeadSource.Facebook);
                                        var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                                        try
                                        {
                                            await _leadHistoryRepo.AddAsync(leadHsitory);
                                        }
                                        catch (Exception e)
                                        {

                                        }
                                        #region DuplicateLead History
                                        try
                                        {
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                                            {
                                                var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                                if (totalDuplicateLeads?.Any() ?? false)
                                                {
                                                    await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion

                                        #region Assignment History
                                        try
                                        {
                                            if (lead.AssignTo != Guid.Empty)
                                            {
                                                await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion

                                        #region Push Notification
                                        try
                                        {
                                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                            List<string> notificationResponses = new();
                                            string? tenantId = await _npgsqlRepo.GetTenantId(integrationAccountInfo?.Id ?? default);
                                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                            if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                                            {
                                                _logger.Information($"ProcessFacebookWebhookRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                                if (adminIds.Any())
                                                {
                                                    List<string> notificationScheduleResponse = new();
                                                    if (_isDupicateUnassigned)
                                                    {
                                                        notificationScheduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateUnAssigment, lead, null, null, null, null, null, adminIds);
                                                    }
                                                    else
                                                    {
                                                        notificationScheduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, null, null, null, null, null, adminIds);
                                                    }
                                                    notificationResponses.AddRange(notificationScheduleResponse);
                                                }
                                            }
                                            else if (lead.AssignTo != Guid.Empty)
                                            {
                                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(lead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"ProcessFacebookWebhookRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                            Console.WriteLine($"ProcessFacebookWebhookRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && ((userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false) || (formData?.UserAssignment?.IsDuplicateAssignmentEnabled ?? false)))
                                            {
                                                var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                                if (allduplicateLeads?.Any() ?? false)
                                                {
                                                    foreach (var duplicatelead in allduplicateLeads)
                                                    {
                                                        try
                                                        {
                                                            if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                                            {
                                                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                                                if (user != null)
                                                                {
                                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                                }
                                                                List<Guid> userWithManagerIds = new();
                                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                                {
                                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                                                    userWithManagerIds.AddRange(managerIds);
                                                                }
                                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                                {
                                                                    userWithManagerIds.AddRange(adminIds);
                                                                }
                                                                if (user != null && userWithManagerIds.Any())
                                                                {
                                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                                    userWithManagerIds.Remove(duplicatelead.AssignTo);
                                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                                }
                                                            }
                                                            _logger.Information($"ProcessFacebookWebhookRequestHandler -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                                        }
                                                        catch (Exception ex)
                                                        {
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Information($"ProcessFacebookWebhookRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            Console.WriteLine($"ProcessFacebookWebhookRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                        }
                                        #endregion

                                        #region Lead Rotation
                                        try
                                        {
                                            if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                                            {
                                                if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                                {
                                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: ad?.Id ?? formData?.Id ?? integrationAccountInfo?.Id);
                                                }
                                            }
                                            else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                            {
                                                if (lead.AssignTo != Guid.Empty)
                                                {
                                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: ad?.Id ?? formData?.Id ?? integrationAccountInfo?.Id);
                                                }
                                            }
                                        }
                                        catch { }
                                        #endregion
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                _logger.Error("ProcessFacebookWebhookRequestHandler -> Error: " + JsonConvert.SerializeObject(e));
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ProcessFacebookWebhookRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                            _logger.Information("Finished processing facebook webhook request");
                        }
                        else if (ad != null && !ad.IsSubscribed)
                        {
                            _logger.Information($"lead is not stored because the ad id: {adId} is not subscribed in Tenant: {request.TenantId} ");
                            Console.WriteLine($"lead is not stored because the ad id: {adId} is not subscribed in Tenant: {request.TenantId} ");
                            ad.IsSubscribed = true;
                            await _fbAdsInfoRepo.UpdateAsync(ad);
                            request.RotationCounter++;
                            return await Handle(request, default);
                        }
                        else if (formData != null && !formData.IsSubscribed)
                        {
                            _logger.Information($"lead is not stored because the form id: {formId} is not subscribed in Tenant: {request.TenantId} ");
                            Console.WriteLine($"lead is not stored because the form id: {formId} is not subscribed in Tenant: {request.TenantId} ");
                            formData.IsSubscribed = true;
                            await _facebookLeadGenFormRepo.UpdateAsync(formData);
                            request.RotationCounter++;
                            return await Handle(request, default);
                        }
                        else if (ad == null || formData == null)
                        {
                            if (!string.IsNullOrWhiteSpace(adId))
                            {
                                await UpdateAdsDetailsInDB(fbAuthResponse.LongLivedUserAccessToken, fbAuthResponse.Id);
                                request.RotationCounter++;
                                return await Handle(request, default);
                            }
                            else if (!string.IsNullOrWhiteSpace(formId))
                            {
                                await UpdateLeadgenForms(fbPageAccount);
                                request.RotationCounter++;
                                return await Handle(request, default);
                            }
                            else
                            {
                                _logger.Information($"lead is not stored because ad not found by the ad id {adId} and no form found by the form id {formId} in Tenant: {request.TenantId} ");
                                Console.WriteLine($"lead is not stored because ad not found by the ad id {adId} and no form found by the form id {formId} in Tenant: {request.TenantId} ");
                            }
                        }

                        _logger.Information($"**********************************  Finished Facebook - {request.TenantId} ************************************");
                        Console.WriteLine($"**********************************  Finished Facebook - {request.TenantId} ************************************");

                        return new(true, "Successful");
                    }
                    catch (Exception e)
                    {
                        _logger.Error("ProcessFacebookWebhookRequestHandler -> Error: " + JsonConvert.SerializeObject(e));
                        var error = new LrbError()
                        {
                            ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                            ErrorSource = e?.Source,
                            StackTrace = e?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "ProcessFacebookWebhookRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        await _facebookService.SendErrorMessageAsync(true, request.Dto, e);
                        _logger.Information($"**********************************  Finished Facebook - {request.TenantId} ************************************");
                        Console.WriteLine($"**********************************  Finished Facebook - {request.TenantId} ************************************");

                        return new(false, JsonConvert.SerializeObject(e));

                    }
                }
                else
                {
                    return new(false, "Tried for two times, but no active ads or forms found.");
                }
            }
            catch (Exception ex)
            {
                _logger.Information($"ProcessFacebookWebhookRequestHandler -> Handle -> Something Went Wrong {JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                return new(false, $"$\"ProcessFacebookWebhookRequestHandler -> Handle -> Something Went Wrong {JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            }

        }

        private async Task<Domain.Entities.Lead> AutomateLeadAsync(Domain.Entities.Lead lead, Guid? automationId, List<Domain.Entities.Lead> duplicateLeads, DuplicateLeadFeatureInfo? duplicateFeatureInfo)
        {

            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead before assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Mapped Lead before assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            var assignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(automationId ?? Guid.Empty);

            _logger.Information("Direct Assignment Info: " + JsonConvert.SerializeObject(assignmentInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            Console.WriteLine("Direct Assignment Info: " + JsonConvert.SerializeObject(assignmentInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));

            if (assignmentInfo?.ProjectIds?.Any() ?? false)
            {
                var project = (await _projectRepo.ListAsync(new GetAllProjectsV2Spec(assignmentInfo?.ProjectIds ?? new()), CancellationToken.None))?.FirstOrDefault();
                _logger.Information("Project Info: " + JsonConvert.SerializeObject(project, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                Console.WriteLine("Project Info: " + JsonConvert.SerializeObject(project, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                lead.Projects ??= new List<Lrb.Domain.Entities.Project>();
                if (project != null)
                {
                    lead.Projects.Add(project);
                    _logger.Information("lead after Project added: " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                    Console.WriteLine("Lead after Project added: " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                    if (project.IsAutomated || project.AutomationId != default)
                    {
                        var projAssignmentInfo = await _integrationAssignmentInfoRepo.GetByIdAsync(project.AutomationId);

                        _logger.Information("Project Assignment Info: " + JsonConvert.SerializeObject(projAssignmentInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        Console.WriteLine("Project Assignment Info: " + JsonConvert.SerializeObject(projAssignmentInfo, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));

                        if (projAssignmentInfo?.AssignedUserIds?.Any() ?? false)
                        {
                            assignmentInfo = projAssignmentInfo;
                        }
                    }
                }
            }

            if (duplicateFeatureInfo != null && !duplicateFeatureInfo.AllowAllDuplicates)
            {
                var assignedUserIds = assignmentInfo?.AssignedUserIds;
                if (assignedUserIds?.Any() ?? false)
                {
                    if (duplicateLeads?.Any() ?? false)
                    {
                        duplicateLeads = duplicateLeads.Where(i => i.AssignTo != Guid.Empty).ToList();
                        var allMatched = assignedUserIds.All(i => duplicateLeads.Select(j => j.AssignTo).Any(j => j == i));
                        if (!allMatched)
                        {
                            foreach (var id in assignedUserIds)
                            {
                                lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);

                                if (!duplicateLeads?.Any(i => i.AssignTo == lead.AssignTo) ?? false)
                                {
                                    break;
                                }
                            }
                            if (duplicateLeads?.Any(i => i.AssignTo == lead.AssignTo) ?? false)
                            {
                                _logger.Information("Existing Lead already assigned to same user found.");
                                Console.WriteLine("Existing Lead already assigned to same user found.");
                                lead.AssignTo = Guid.Empty;
                                _isDupicateUnassigned = true;
                            }
                        }
                        else
                        {
                            _isDupicateUnassigned = true;
                        }
                    }
                    else
                    {
                        lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);
                    }
                }
            }
            else
            {
                lead = await lead.GetAssignedLead(assignmentInfo?.Id ?? Guid.Empty, _integrationAssignmentInfoRepo, _userDetailsRepo, _userService);
            }
            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            Console.WriteLine("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            return lead;
        }
    }
}
