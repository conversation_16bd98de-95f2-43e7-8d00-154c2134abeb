﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.V3;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Lead.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile.Requests
{
    public class V2AssignLeadsBasedOnScenariosRequest : LeadAssignmentDto, IRequest<Response<bool>>
    {
        public bool CreateDuplicate { get; set; }
    }
    public class V2AssignLeadsBasedOnScenariosRequestHandler : IRequestHandler<V2AssignLeadsBasedOnScenariosRequest, Response<bool>>
    {
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IUserService _userService;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly ICurrentUser _currentUser;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        protected readonly IRepositoryWithEvents<LeadCommunication> _communicationRepo;
        protected readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly INotificationSenderService _notificationSenderService;
        private readonly IDapperRepository _dapperRepository;

        public V2AssignLeadsBasedOnScenariosRequestHandler(IMediator mediator, IRepositoryWithEvents<Domain.Entities.Lead> leadRepo, IUserService userService,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo, ICurrentUser currentUser, IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo, IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<LeadCommunication> communicationRepo, IRepositoryWithEvents<LeadAppointment> appointmentRepo, INpgsqlRepository npgsqlRepo,
            INotificationSenderService notificationSenderService, IDapperRepository dapperRepository)
        {
            _mediator = mediator;
            _leadRepo = leadRepo;
            _userService = userService;
            _leadHistoryRepo = leadHistoryRepo;
            _currentUser = currentUser;
            _projectRepo = projectRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _appointmentRepo = appointmentRepo;
            _communicationRepo = communicationRepo;
            _npgsqlRepo = npgsqlRepo;
            _notificationSenderService = notificationSenderService;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<bool>> Handle(V2AssignLeadsBasedOnScenariosRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.CreateDuplicate)
                {
                    var duplicateRequest = request.Adapt<Web.Requests.AssignAndCreateDuplicateLeadsRequest>();
                    await _mediator.Send(duplicateRequest);
                    return new(true);
                }
                var lead = await _leadRepo.FirstOrDefaultAsync(new LeadsByIdsSpec(request.LeadIds ?? new()));
                if (lead != null)
                {
                    var user = (await _userService.GetListOfUsersByIdsAsync(request.UserIds?.Select(i => i.ToString()).ToList(), cancellationToken)).FirstOrDefault();
                    var (Leads, SkippedLeadsInfo) = await ReassignLeadsAsync(lead, request, user, cancellationToken, currentUserId: request.CurrentUserId,tenantId:request.TenantId);
                    return new(true);
                }
                return new(true);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        private List<LeadAppointment> GetLeadAppointmentsToAdd(List<LeadAppointment> leadAppointments, Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var leadAppointmentsAssignTo = leadAppointments.Where(i => i.UserId == lead.AssignTo).DistinctBy(comm => comm.UniqueKey).ToList();
            var leadAppointmentsAssignFrom = leadAppointments.Where(i => i.UserId == lead.AssignedFrom).DistinctBy(comm => comm.UniqueKey).ToList();
            List<LeadAppointment> leadAppointmentsToAdd = new();
            leadAppointmentsAssignFrom.ForEach(appt =>
            {
                var leadAppt = leadAppointmentsAssignTo.FirstOrDefault(subAppt =>
                                                    (subAppt.Type == appt.Type
                                                 && subAppt.IsDone == appt.IsDone
                                                 && subAppt.Longitude == appt.Longitude
                                                 && subAppt.Latitude == appt.Latitude
                                                 && subAppt.LocationId == appt.LocationId
                                                 && subAppt.CreatedBy == appt.CreatedBy
                                                 && subAppt.CreatedOn == appt.CreatedOn
                                                 && subAppt.ProjectName == appt.ProjectName
                                                 && subAppt.IsFullyCompleted == appt.IsFullyCompleted
                                                 && subAppt.LastModifiedBy == appt.LastModifiedBy
                                                 && subAppt.LastModifiedOn == appt.LastModifiedOn
                                                 && subAppt.LeadId == appt.LeadId
                                                 && subAppt.UniqueKey == appt.UniqueKey));
                if (leadAppt == null)
                {
                    leadAppointmentsToAdd.Add(appt);
                }
            });
            return leadAppointmentsToAdd;
        }
        private List<LeadCommunication> GetLeadCommsToAdd(List<LeadCommunication> leadCommunications, Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var leadCommsAssignTo = leadCommunications.Where(i => i.UserId == lead.AssignTo).DistinctBy(comm => comm.UniqueKey).ToList();
            var leadCommsAssignFrom = leadCommunications.Where(i => i.UserId == lead.AssignedFrom).DistinctBy(comm => comm.UniqueKey).ToList();
            List<LeadCommunication> leadCommsToAdd = new();
            leadCommsAssignFrom.ForEach(comm =>
            {
                var leadComm = leadCommsAssignTo.FirstOrDefault(subComm =>
                                                    (subComm.ContactType == comm.ContactType
                                                 && subComm.CreatedBy == comm.CreatedBy
                                                 && subComm.CreatedOn == comm.CreatedOn
                                                 && subComm.LastModifiedBy == comm.LastModifiedBy
                                                 && subComm.LastModifiedOn == comm.LastModifiedOn
                                                 && subComm.LeadId == comm.LeadId
                                                 && subComm.UniqueKey == comm.UniqueKey));
                if (leadComm == null)
                {
                    leadCommsToAdd.Add(comm);
                }
            });
            return leadCommsToAdd;
        }
        public async Task UpdateLeadAppointmentAndCommunicationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.Appointments?.Where(i => i.UserId == lead.AssignedFrom || i.UserId == lead.AssignTo).Any() ?? false)
                {
                    List<LeadAppointment> leadApptAssignFroom = new();
                    var leadApptToAdd = GetLeadAppointmentsToAdd(lead.Appointments.ToList(), lead, cancellationToken);
                    if (leadApptToAdd.Any())
                    {
                        leadApptToAdd.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                        await _appointmentRepo.AddRangeAsync(leadApptToAdd, cancellationToken);
                    }
                }
                var leadcommunications = (await _communicationRepo.ListAsync(new LeadCommunicationSpec(lead.Id, lead.AssignedFrom, lead.AssignTo), cancellationToken));
                var leadCommToAdd = GetLeadCommsToAdd(leadcommunications, lead, cancellationToken);
                if (leadCommToAdd?.Any() ?? false)
                {
                    List<LeadCommunication> communications = new();
                    communications = leadCommToAdd;
                    communications.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                    await _communicationRepo.AddRangeAsync(communications, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task InitializeLeadStatusAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead != null)
                {
                    var newStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                    lead.CustomLeadStatus = newStatus;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task SetReassignedLeadDetailsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken)
        {
            try
            {
                switch (assignmentDto.AssignmentType)
                {
                    case LeadAssignmentType.WithoutHistory:
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        break;
                    case LeadAssignmentType.WithoutHistoryWithNewStatus:
                        await InitializeLeadStatusAsync(lead, cancellationToken);
                        lead.ScheduledDate = null;
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        break;
                    case LeadAssignmentType.WithHistory:
                       // await UpdateLeadAppointmentAndCommunicationAsync(lead, cancellationToken);
                        break;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task SetReassignedLeadEnquiryAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.Enquiries != null && lead.Enquiries.Any())
                {
                    var primaryEnquiry = lead.Enquiries.FirstOrDefault(e => e.IsPrimary);
                    if (primaryEnquiry != null)
                    {
                        if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                        {
                            primaryEnquiry.SubSource = assignmentDto.SubSource;
                        }
                        if (assignmentDto.UpdateSource)
                        {
                            primaryEnquiry.LeadSource = assignmentDto.LeadSource;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        protected async Task<(Domain.Entities.Lead? Leads, List<Web.DuplicateLeadAssigmentResponseDto> SkippedLeadsInfo)> ReassignLeadsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, UserDetailsDto? user, CancellationToken cancellationToken = default, Guid? currentUserId = null,string? tenantId = null)
        {
            try
            {
                if (user != null && lead != null)
                {
                    await SetReassignedLeadEnquiryAsync(lead, assignmentDto, cancellationToken);

                    if (assignmentDto.UpdateProject && assignmentDto.Projects != null && assignmentDto.Projects.Any())
                    {
                        await SetLeadProjectsAsync(lead, assignmentDto.Projects, cancellationToken);
                    }

                    await SetReassignedLeadDetailsAsync(lead, assignmentDto, cancellationToken);

                    await _leadRepo.UpdateAsync(lead, cancellationToken);

                    await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, new List<UserDetailsDto>() { user }, cancellationToken, currentUserId,previousAssignedUser:assignmentDto.PreviousAssignedUser);

                    await SendLeadAssignmentNotificationsAsync(lead, cancellationToken,currentUserId:currentUserId,tenantId:tenantId);
                }
                else if(lead != null && (assignmentDto.PreviousAssignedUser != Guid.Empty && assignmentDto.PreviousAssignedUser != null ))
                {
                    await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, new List<UserDetailsDto>() { user }, cancellationToken, currentUserId, previousAssignedUser: assignmentDto.PreviousAssignedUser);
                }
                return new(lead, new());
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, Guid? currentUserId = null,string? tenantId = null)
        {
            try
            {
                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != currentUserId)
                {
                    var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                    var userInfo = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { lead.AssignTo }))?.FirstOrDefault();
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty)).Where(i => i != (currentUserId)).ToList();
                    var globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                    var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty) ?? new();
                    if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
                    {
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: adminIds);
                    }
                    if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo != null) && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo?.ReportsTo.Id != currentUserId))
                    {
                        List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
                    }
                    if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo != null) && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.GeneralManager.Id == i)) && (userInfo.GeneralManager.Id != currentUserId))
                    {
                        List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
                    }


                }
                else if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != currentUserId).ToList();
                    if (adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            if (adminDetails != null)
                            {
                                List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: 1);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task SetLeadProjectsAsync(Domain.Entities.Lead lead, List<string>? projectList, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);
                var country = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = await _projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken);
                        if (existingProject != null)
                        {
                            projects.Add(existingProject);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Project project = new()
                            {
                                Name = newProject,
                                MonetaryInfo = new ProjectMonetaryInfo
                                {
                                    Currency = country
                                }
                            };
                            project = await _projectRepo.AddAsync(project, cancellationToken);
                            projects.Add(project);
                        }
                    }
                    lead.Projects = projects;
                }
                else if ((lead?.Projects?.Any() ?? false) && projectList == null)
                {
                    lead.Projects = projects;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task UpdateReassignedLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, List<UserDetailsDto> users, CancellationToken cancellationToken = default, Guid? currentUserId = null,Guid? previousAssignedUser = null)
        {
            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedUser ?? currentUserId ?? Guid.Empty)); ;
            try
            {
                if (currentUserId != null && currentUserId != default)
                {
                    var currentUser = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, cancellationToken);
                    if (currentUser != null && users.Any(i => i.Id != currentUser.Id))
                    {
                        users.Add(currentUser);
                    }
                }
            }
            catch (Exception ex) { }
            var leadDto = lead.Adapt<ViewLeadDto>();
            if (leadDto != null)
            {
                leadDto.AssignmentType = assignmentType;
            }
            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken,currentUserId);
            var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            if (existingLeadHistory != null)
            {
           
                 await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory));                           
            }
            else
            {
                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                if (existingLeadHistory != null)
                {
                    switch (assignmentType)
                    {
                        case LeadAssignmentType.WithoutHistory:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithoutHistoryWithNewStatus:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithHistory:
                            await KeepOldHistoryForLeadReassignmentAsync(existingLeadHistory, leadDto, users, cancellationToken);
                            break;
                    }
                }
                else if (existingLeadHistory == null)
                {
                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken);
                }
            }
        }
        protected async Task AddNewHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, LeadHistory newHistory, List<UserDetailsDto> users, ViewLeadDto fullLeadDto, bool withNewStatus, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUser.GetUserId()));
                if (currentUser == null)
                {
                    try
                    {
                        currentUser = (await _userService.GetListOfUsersByIdsAsync(new() { (currentUserId ?? _currentUser.GetUserId()).ToString() }, cancellationToken)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignedFrom ?? Guid.Empty));
                if (assignedFromUser == null && fullLeadDto.AssignedFrom != Guid.Empty)
                {
                    try
                    {
                        assignedFromUser = (await _userService.GetListOfUsersByIdsAsync(new() { (fullLeadDto?.AssignedFrom ?? Guid.Empty).ToString() }, cancellationToken)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var assignToUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignTo ?? Guid.Empty));
                var leadHistory = existingLeadHistory.MapV1LeadHistory(currentUser, assignedFromUser, assignToUser, withNewStatus);
                leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newHistory, leadHistory);
                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

                leadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser);
                if (leadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(leadHistory, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<LeadHistory?> UpdateHistoryForLeadReassignmentAsync(LeadHistory leadHistory, UserDetailsDto? assignedFromUser, UserDetailsDto? assignToUser, UserDetailsDto? currentUser)
        {
            try
            {
                if (leadHistory != null)
                {
                    var version = leadHistory.CurrentVersion + 1;
                    leadHistory.LastModifiedBy?.Add(version, $"{currentUser?.FirstName} {currentUser?.LastName}" ?? string.Empty);
                    leadHistory.AssignedTo?.Add(version, assignToUser?.Id ?? default);
                    leadHistory.AssignedFromUser?.Add(version, $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}" ?? string.Empty);
                    leadHistory.AssignedToUser?.Add(version, $"{assignToUser?.FirstName} {assignToUser?.LastName}" ?? string.Empty);
                    leadHistory.ModifiedDate?.Add(version, DateTime.UtcNow);
                    leadHistory.CurrentVersion = version;
                }
                return leadHistory;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                return leadDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        protected async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            try
            {
                var userId = currentUserId ?? _currentUser.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
