﻿using Amazon.Batch;
using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.SMS;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadRotation.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.QRFormTemplate.Web.Specs;
using Lrb.Application.Reports.Web;
using Lrb.Application.Team.Web;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Shared.Utils;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Npgsql;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net;
using System.Reflection;
using System.Threading;

namespace Lrb.Application.Lead.Web.Requests
{
    public class LeadCommonRequestHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly string _className;
        private readonly string _methodName;
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        //protected readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        protected readonly IRepositoryWithEvents<PropertyDimension> _dimensionRepo;
        protected readonly IRepositoryWithEvents<LeadTag> _leadTagRepo;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        protected readonly ICurrentUser _currentUserRepo;
        protected readonly IUserService _userService;
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly Common.NotificationService.INotificationService _notificationService;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IEmailService _emailService;
        protected readonly ITextLocalSmsService _textLocalSmsService;
        protected readonly IHostEnvironment _environment;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ICurrentUser _currentUser;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        protected readonly IRepositoryWithEvents<Location> _locationRepo;
        protected readonly IRepositoryWithEvents<LeadCommunication> _communicationRepo;
        protected readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _cpRepository;
        protected readonly IRepositoryWithEvents<Flag> _flagRepository;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly Serilog.ILogger _logger;
        protected readonly IRepositoryWithEvents<WhatsAppCommunication> _whatsAppCommunicationRepo;
        protected readonly IRepositoryWithEvents<LeadBookedDetail> _leadBookedDetailRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.QRFormTemplate> _qrFormTemplateRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Templates.QRForm.QRAssignment> _qrAssignmentRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        protected readonly IServiceBus _serviceBus;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly ILeadRotationService _leadRotationService;
        protected readonly IRepositoryWithEvents<LeadsAssignRotationInfo> _leadRotationRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepository;
        protected readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;

        private bool _isDupicateUnassigned = false;
        protected readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.TempProjects> _tempProjectRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitType;
        protected readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.LeadRotationTracker> _rotationTrackerRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Campaign> _campaignRepo;
        protected readonly IRepositoryWithEvents<FacebookAuthResponse> _fbAuthResponseRepo;

        public LeadCommonRequestHandler(
            IServiceProvider serviceProvider,
            string className,
            string methodName)

        {
            _serviceProvider = serviceProvider;
            _className = className;
            _methodName = methodName;
            _leadRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Lead>>();
            _masterAreaUnitRepo = _serviceProvider.GetRequiredService<IReadRepository<MasterAreaUnit>>();
            _leadEnquiryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadEnquiry>>();
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _customLeadStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterLeadStatus>>();
            //_leadStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterLeadStatus>>();
            _dimensionRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyDimension>>();
            _leadTagRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadTag>>();
            _leadHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadHistory>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _currentUserRepo = _serviceProvider.GetRequiredService<ICurrentUser>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _notificationService = _serviceProvider.GetRequiredService<Common.NotificationService.INotificationService>();
            _propertyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _emailService = _serviceProvider.GetRequiredService<IEmailService>();
            _textLocalSmsService = _serviceProvider.GetRequiredService<ITextLocalSmsService>();
            _environment = _serviceProvider.GetRequiredService<IHostEnvironment>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _currentUser = _serviceProvider.GetRequiredService<ICurrentUser>();
            _mediator = _serviceProvider.GetRequiredService<IMediator>();
            _duplicateInfoRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<DuplicateLeadFeatureInfo>>();
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Location>>();
            _communicationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadCommunication>>();
            _appointmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAppointment>>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _cpRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner>>();
            _flagRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Flag>>();
            _npgsqlRepo = _serviceProvider.GetRequiredService<INpgsqlRepository>();
            _logger = _serviceProvider.GetRequiredService<Serilog.ILogger>();
            _leadBookedDetailRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadBookedDetail>>();
            _whatsAppCommunicationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<WhatsAppCommunication>>();
            _qrFormTemplateRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.QRFormTemplate>>();
            _assignmentModuleRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<AssignmentModule>>();
            _qrAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Templates.QRForm.QRAssignment>>();
            _globalSettingsRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _userAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UserAssignment>>();
            _userDetailsRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.UserDetails>>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Project>>();
            _propertyRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _tempProjectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.TempProjects>>();
            _serviceBus = _serviceProvider.GetRequiredService<IServiceBus>();
            _leadRotationService = _serviceProvider.GetRequiredService<ILeadRotationService>();
            _leadRotationRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadsAssignRotationInfo>>();
            _userDetailsRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.UserDetails>>();
            _agencyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Agency>>();
            _unitType = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UnitType>>();
            _teamRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Team>>();
            _rotationTrackerRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadRotationTracker>>();
            _leadAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAssignment>>();
            _campaignRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Campaign>>();
            _fbAuthResponseRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<FacebookAuthResponse>>();
        }

        protected async Task<Domain.Entities.Lead?> GetExistingLeadAsync(Guid id, string contactNo, CancellationToken cancellationToken = default)
        {
            try
            {
                var existingFullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(id), cancellationToken) ?? throw new NotFoundException("No Lead found by this Id");
                //var duplicateLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { contactNo.Substring(contactNo.Length - 10) }), cancellationToken);
                //if (duplicateLeads != null && duplicateLeads.Any(i => i.Id != existingFullLead.Id))
                //{
                //    throw new DuplicateLeadException("Duplicate Lead Found With Ids : " + JsonConvert.SerializeObject(duplicateLeads.Where(i => i.Id != existingFullLead.Id).Select(i => i.Id).ToArray()));
                //}
                return existingFullLead;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                leadDto.LastModifiedBy = currentUserId ?? Guid.Empty;
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
                return leadDto;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        public async Task<(LeadHistory? New, LeadHistory? Old)> GetUpdatedLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            try
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                if (existingLeadHistory != null)
                {
                    var updatedLeadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory);
                    return (null, updatedLeadHistory);
                }
                else
                {
                    return (newLeadHistory, null);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<List<Domain.Entities.Lead>> SearchForDuplicateLeadsAsync(DuplicateLeadSpecDto lead, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Domain.Entities.Lead> duplicateLeads = new();
                var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                {
                    if (!duplicateFeatureInfo.AllowAllDuplicates)
                    {
                        duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, lead), cancellationToken);
                        if (duplicateLeads.Any())
                        {
                            bool isDuplicate = true;
                            try
                            {
                                if (duplicateFeatureInfo.IsProjectBased && lead?.ProjectsList?.Count > 1)
                                {
                                    isDuplicate = lead.ProjectsList.All(item => duplicateLeads.Any(i => i.Projects != null && i.Projects.Select(i => i.Name?.ToLower() ?? string.Empty).Contains(item.ToLower())));
                                }
                            }
                            catch (Exception ex)
                            {
                                isDuplicate = false;
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                            if (isDuplicate)
                            {
                                JsonSerializerSettings settings = new()
                                {
                                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                                };
                                throw new Exception("Lead with Same Enquiry is already present->> " + JsonConvert.SerializeObject(duplicateLeads, settings));
                            }
                        }
                    }
                }
                else
                {
                    duplicateLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo != null && lead.ContactNo.Length >= 10 ? lead.ContactNo.Substring(lead.ContactNo.Length - 10) : "Invalid Number" }), cancellationToken);
                    if (duplicateLeads.Any())
                    {
                        JsonSerializerSettings settings = new()
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                        };
                        throw new Exception("Duplicate Lead ->> " + JsonConvert.SerializeObject(duplicateLeads, settings));
                    }
                }
                return duplicateLeads;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<Domain.Entities.Lead> InitializeLeadAsync(Domain.Entities.Lead lead, LeadTag? leadTags = null, Address? address = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead ??= new();
                await InitializeLeadStatusAsync(lead, cancellationToken);
                await SetLeadNameAndNumberAsync(lead, cancellationToken);
                leadTags ??= new();
                if (leadTags != null)
                {
                    lead.TagInfo = leadTags;
                }
                if (address != null)
                {
                    lead.Address = address;
                }
                else
                {
                    lead.Address = null;
                }

                // Set the OriginalOwner to the current AssignTo value when creating a new lead
                if (lead.OriginalOwner == null && lead.AssignTo != Guid.Empty)
                {
                    lead.OriginalOwner = lead.AssignTo;
                }

                return lead;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<Domain.Entities.Lead> InitializeDuplicateLeadAsync(ViewLeadDto leadDto, Identity.Users.UserDetailsDto user, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Guid rootId = leadDto.RootId.HasValue ? leadDto.RootId.Value : leadDto.Id;
                Guid parentLeadId = leadDto.Id;
                var duplicateLead = leadDto.Adapt<Domain.Entities.Lead>();
                duplicateLead.Id = Guid.NewGuid();
                duplicateLead.CreatedBy = default;
                duplicateLead.ChildLeadsCount = 0;
                duplicateLead.DuplicateLeadVersion = null;
                duplicateLead.SecondaryUserId = null;
                duplicateLead.LastModifiedBy = default;
                await UpdateDuplicateVersionAsync(duplicateLead, leadDto.Id, cancellationToken);
                duplicateLead.Projects = new List<Lrb.Domain.Entities.Project>();
                duplicateLead.Properties = new List<Domain.Entities.Property>();
                duplicateLead.AssignTo = user.Id;
                duplicateLead.Appointments = null;
                duplicateLead.CustomFlags = null;
                duplicateLead.Enquiries = new List<LeadEnquiry>();

                if (assignmentDto.AssignmentType == LeadAssignmentType.WithoutHistoryWithNewStatus)
                {
                    await InitializeLeadStatusAsync(duplicateLead, cancellationToken);
                }
                else
                {
                    string status = string.Empty;
                    if (leadDto?.Status?.ChildType == null)
                    {
                        status = leadDto?.Status?.Status ?? string.Empty;
                    }
                    else
                    {
                        status = leadDto?.Status?.ChildType?.Status ?? string.Empty;
                    }
                    duplicateLead.CustomLeadStatus = !string.IsNullOrWhiteSpace(status) ? await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec(status), cancellationToken) : null;
                    // var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec(new List<Guid>() { duplicateLead.CustomLeadStatus?.Id ?? Guid.Empty }), cancellationToken);
                    //if (customStatus?.MasterLeadStatusId != null)
                    //{
                    //    duplicateLead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(customStatus.MasterLeadStatusId.Value), cancellationToken);
                    //}
                    //else
                    //{
                    //    duplicateLead.Status = null;
                    //}
                }
                if (!(assignmentDto.AssignmentType == LeadAssignmentType.WithHistory))
                {
                    duplicateLead.Notes = null;
                    duplicateLead.ConfidentialNotes = null;
                    duplicateLead.Rating = null;
                    duplicateLead.ScheduledDate = null;
                    duplicateLead.TagInfo = new LeadTag();
                    duplicateLead.CustomFlags = null;
                }
                return duplicateLead;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task InitializeLeadStatusAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, CustomMasterLeadStatus? newStatus = null)
        {
            try
            {
                if (lead != null && newStatus != null)
                {
                    //var newStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                    lead.CustomLeadStatus = newStatus;

                    //if (newStatus?.MasterLeadStatusId != null)
                    //{
                    //    lead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(newStatus.MasterLeadStatusId.Value), cancellationToken);
                    //}
                }
                else if (lead != null)
                {
                    newStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                    lead.CustomLeadStatus = newStatus;

                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task CreateLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                await _leadHistoryRepo.AddAsync(leadHistory);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = location.MapToAddress();
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                {
                    //if (newAddress != null)
                    //{
                    //    address = await _addressRepo.AddAsync(newAddress ?? new());
                    //    await MapAddressToLocationAndSaveAsync(address);
                    //}
                    if (newAddress != null)
                    {
                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                        if (existingAddress == null)
                        {
                            address = await _addressRepo.AddAsync(newAddress);
                        }
                        else
                        {
                            address = existingAddress;
                        }
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                return address;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<List<Address>?> CreateAddressesAsync(List<AddressDto>? addressDtos, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Address?> addresses = new List<Address?>();
                if (addressDtos != null)
                {
                    foreach (var addressDto in addressDtos)
                    {
                        Address? address = null;

                        if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty )
                        {
                            address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                            if (address == null)
                            {
                                var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                                if (location != null)
                                {
                                    address = location.MapToAddress();
                                    if (address != null)
                                    {
                                        address.Id = Guid.Empty;
                                        address = await _addressRepo.AddAsync(address);
                                    }
                                }
                                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                                {
                                    if (newAddress != null )
                                    {
                                        newAddress.LocationId = null;
                                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                                        if (existingAddress == null)
                                        {
                                            address = await _addressRepo.AddAsync(newAddress);
                                        }
                                        else
                                        {
                                            address = existingAddress;
                                        }
                                        await MapAddressToLocationAndSaveAsync(address);
                                    }
                                }
                            }

                        }
                        else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                        {
                            address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                            if (address == null)
                            {
                                try
                                {
                                    address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                                }
                                catch (Exception ex)
                                {
                                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                }
                                if (address != null)
                                {
                                    address = await _addressRepo.AddAsync(address);
                                    await MapAddressToLocationAndSaveAsync(address);
                                }
                            }
                        }
                        else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                        {
                            var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                            var place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address == null)
                                {
                                    try
                                    {
                                        address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex)
                                    {
                                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                    }
                                    if (address != null)
                                    {
                                        address = await _addressRepo.AddAsync(address);
                                        await MapAddressToLocationAndSaveAsync(address);
                                    }
                                }
                            }
                            else if (place != null)
                            {
                                address = place.Adapt<Address>();
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                        {
                            if (newAddress != null)
                            {
                                var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                                if (existingAddress == null)
                                {
                                    address = await _addressRepo.AddAsync(newAddress);
                                }
                                else
                                {
                                    address = existingAddress;
                                }
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        if (address != null)
                        {
                            addresses.Add(address);
                        }
                    }
                }
                return addresses;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<LeadTag> CreateLeadTagsAsync(LeadTagDto? leadTags)
        {
            try
            {
                return leadTags?.Adapt<LeadTag>() ?? new();

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<(List<Domain.Entities.Lead> Leads, List<LeadHistory> NewHistories, List<LeadHistory> OldHistories)> ArchiveLeadsAsync(List<Guid> leadIds, CancellationToken cancellationToken)
        {
            try
            {
                (List<Domain.Entities.Lead> Leads, List<LeadHistory> NewHistories, List<LeadHistory> OldHistories) leadData = new() { Leads = new(), NewHistories = new(), OldHistories = new() };

                var currentUserId = _currentUser.GetUserId();

                var leads = await _leadRepo.ListAsync(new LeadsByIdsSpec(leadIds), cancellationToken);
                var existingLeadHistories = await _leadHistoryRepo.ListAsync(new LeadHistorySpec(leadIds), cancellationToken);
                if (leads?.Any() ?? false)
                {
                    foreach (var lead in leads)
                    {
                        if (lead.AssignTo == currentUserId)
                        {
                            lead.ShouldUpdatePickedDate = true;
                        }
                        var existingLeadHistory = existingLeadHistories?.FirstOrDefault(i => i.LeadId == lead.Id && i.UserId == lead.AssignTo);
                        lead.BulkCategory = BulkType.BulkDelete;
                        var (Lead, NewHistory, OldHistory) = await ArchiveLeadAsync(lead, currentUserId, cancellationToken, existingLeadHistory);

                        leadData.Leads.Add(Lead);
                        if (NewHistory != null)
                        {
                            leadData.NewHistories.Add(NewHistory);
                        }
                        else if (OldHistory != null)
                        {
                            leadData.OldHistories.Add(OldHistory);
                        }
                    }
                    return leadData;
                }
                else
                {
                    throw new NotFoundException("No leads found by the provided ids.");
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(Domain.Entities.Lead Lead, LeadHistory? NewHistory, LeadHistory? OldHistory)> ArchiveLeadAsync(Domain.Entities.Lead lead, Guid currentUserId, CancellationToken cancellationToken, LeadHistory? existingLeadHistory = null)
        {
            //try
            //{
            //    var pickedLeadDto = lead.Adapt<PickedLeadDto>();
            //    pickedLeadDto.IsArchived = true;
            //    lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, pickedLeadDto);
            //}
            //catch (Exception ex)
            //{
            //    throw;
            //}
            try
            {
                lead.IsArchived = true;
                lead.ArchivedBy = currentUserId;
                lead.ArchivedOn = DateTime.UtcNow;
                lead.LastModifiedOn = DateTime.UtcNow;
                lead.LastModifiedBy = currentUserId;
                var (New, Old) = await GetUpdatedLeadHistoryForDeleteLeadsAsync(lead, cancellationToken, existingLeadHistory);

                return (lead, New, Old);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public async Task<(LeadHistory? New, LeadHistory? Old)> GetUpdatedLeadHistoryForDeleteLeadsAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, LeadHistory? existingLeadHistory = null)
        {
            try
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                //var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                if (existingLeadHistory != null)
                {
                    var updatedLeadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory);
                    return (null, updatedLeadHistory);
                }
                else
                {
                    return (newLeadHistory, null);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<(List<Domain.Entities.Lead> Leads, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsDtos, List<LeadHistory> NewHistories, List<LeadHistory> OldHistories)> CreateDuplicateLeadsAsync(List<Domain.Entities.Lead> leads, LeadAssignmentDto assignmentDto, List<UserDetailsDto> users, CancellationToken cancellationToken = default)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                List<Domain.Entities.Lead> duplicateLeads = new();
                List<LeadHistory> newHistories = new();
                List<LeadHistory> oldHistories = new();
                List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();

                foreach (var user in users)
                {
                    var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                    {
                        User = new()
                        {
                            Id = user.Id,
                            Name = user.FirstName + " " + user.LastName
                        },
                        Leads = new()
                    };

                    List<Domain.Entities.Lead> userBasedDuplicateLeads = new();
                    List<LeadHistory> userBasedNewHistories = new();
                    List<LeadHistory> userBasedOldHistories = new();

                    foreach (Domain.Entities.Lead lead in leads)
                    {
                        var alreadyAssignedLeads = await _leadRepo.ListAsync(new DuplicateLeadsForAssignmentSpec(lead.ContactNo, user.Id), cancellationToken);
                        if (alreadyAssignedLeads != null && alreadyAssignedLeads.Any())
                        {
                            skippedLeadsDto.Leads.Add(alreadyAssignedLeads[0].Adapt<DuplicateAssigmentLeadDto>());
                        }
                        else
                        {
                            var duplicateLeadInfo = await CreateDuplicateLeadAsync(lead, assignmentDto, user, globalSettings, cancellationToken);

                            userBasedDuplicateLeads.Add(duplicateLeadInfo.Lead);
                            if (duplicateLeadInfo.OldHistory != null)
                            {
                                userBasedOldHistories.Add(duplicateLeadInfo.OldHistory);
                                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory, null, cancellationToken, true);
                            }
                            else if (duplicateLeadInfo.NewHistory != null)
                            {
                                userBasedNewHistories.Add(duplicateLeadInfo.NewHistory);
                                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistory, null, cancellationToken, true);
                            }

                        }
                    }
                    if (userBasedDuplicateLeads.Any())
                    {
                        #region UpdatingPickedDate
                        userBasedDuplicateLeads.ForEach(lead =>
                        {
                            lead.ShouldUpdatePickedDate = false;
                            lead.PickedDate = null;
                            lead.IsPicked = false;
                        });
                        #endregion

                        await _leadRepo.AddRangeAsync(userBasedDuplicateLeads, cancellationToken);

                        await _leadHistoryRepo.AddRangeAsync(userBasedNewHistories, cancellationToken);

                        await _leadHistoryRepo.UpdateRangeAsync(userBasedOldHistories, cancellationToken);

                        await SendLeadAssignmentNotificationsAsync(userBasedDuplicateLeads[0], userBasedDuplicateLeads.Count, globalSettings, cancellationToken);

                        duplicateLeads.AddRange(userBasedDuplicateLeads);
                        newHistories.AddRange(userBasedNewHistories);
                        oldHistories.AddRange(userBasedOldHistories);
                    }
                    skippedLeadsDtos.Add(skippedLeadsDto);
                }
                return (duplicateLeads, skippedLeadsDtos, newHistories, oldHistories);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<(Domain.Entities.Lead Lead, LeadHistory? NewHistory, LeadHistory? OldHistory)> CreateDuplicateLeadAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, UserDetailsDto user, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);

                var duplicateLead = await InitializeDuplicateLeadAsync(leadDto, user, assignmentDto, cancellationToken);

                if (leadDto.Properties != null && leadDto.Properties.Any())
                {
                    await SetTruePropertiesAsync(duplicateLead, leadDto.Properties, cancellationToken);
                }

                if (assignmentDto.UpdateProject && assignmentDto.Projects != null && assignmentDto.Projects.Any())
                {
                    await SetLeadProjectsAsync(duplicateLead, assignmentDto.Projects, globalSettings, cancellationToken);
                }
                else
                {
                    await SetTrueProjectsAsync(duplicateLead, leadDto.Projects, cancellationToken);
                }
                if (duplicateLead.Agencies?.Any() ?? false)
                {
                    var agencies = duplicateLead.Agencies?.Select(i => i.Name ?? string.Empty).ToList();
                    await SetLeadAgencyAsync(duplicateLead, agencies);
                }
                if (duplicateLead.ChannelPartners?.Any() ?? false)
                {
                    var channelPartners = duplicateLead.ChannelPartners?.Select(i => i.FirmName ?? string.Empty).ToList();
                    await SetChannelPartnersAsync(duplicateLead, channelPartners);
                }
                await SetDuplicateLeadEnquiryAsync(duplicateLead, leadDto.Enquiry, assignmentDto, cancellationToken);

                var (New, Old) = await CreateDuplicateLeadHistoryAsync(duplicateLead, assignmentDto.AssignmentType, leadDto.Id, leadDto.AssignTo ?? Guid.Empty, cancellationToken);
                if (duplicateLead.Enquiries?.Any() ?? false)
                {
                    foreach (var item in duplicateLead.Enquiries)
                    {
                        if (item.Address != null)
                        {
                            item.Address = null;
                        }
                    }
                }

                return (duplicateLead, New, Old);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task<(LeadHistory? New, LeadHistory? Old)> CreateDuplicateLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, Guid parentLeadId, Guid parentAssignTo, CancellationToken cancellationToken = default)
        {
            try
            {
                (LeadHistory? New, LeadHistory? Old) historyData = new();
                LeadHistory leadHistory;
                if (assignmentType == LeadAssignmentType.WithHistory)
                {
                    var parentLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(parentLeadId, parentAssignTo));
                    if (parentLeadHistory != null)
                    {
                        leadHistory = parentLeadHistory.Adapt<LeadHistory>();
                        leadHistory.LeadId = lead.Id;
                        leadHistory.UserId = lead.AssignTo;
                        leadHistory.DuplicateLeadVersion = lead.DuplicateLeadVersion;
                        leadHistory.Id = Guid.NewGuid();
                        historyData.New = leadHistory;
                    }
                    else
                    {
                        historyData = await GetUpdatedLeadHistoryAsync(lead, cancellationToken);
                    }
                }
                else
                {
                    lead.Notes = null;
                    lead.Rating = null;
                    lead.ScheduledDate = null;
                    lead.TagInfo = new LeadTag();
                    lead.CustomFlags = null;
                    historyData = await GetUpdatedLeadHistoryAsync(lead, cancellationToken);
                }
                return historyData;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<(List<Domain.Entities.Lead> Leads, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsInfo)> ReassignLeadsAsync(List<Domain.Entities.Lead> leads, LeadAssignmentDto assignmentDto, List<UserDetailsDto>? users, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                if (assignmentDto.UserIds.All(i => i == Guid.Empty))
                {
                    leads = await MakeLeadsAsUnAssignedAsync(leads, cancellationToken, currentUserId);
                    return (leads, new());
                }
                else
                {
                    if (users?.Any() ?? false)
                    {
                        var newStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                        List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();
                        Dictionary<Guid, List<Domain.Entities.Lead>> groupedAssignedLeads = new();
                        int i = 0;
                        int count = 0;
                        List<UserDetailsDto>? userDetails = new();
                        users.ForEach(i => userDetails.Add(i));
                        foreach (var lead in leads)
                        {
                            var user = users[i];
                            lead.BulkCategory = assignmentDto?.BulkCategory ?? BulkType.None;
                            if ((lead.AssignTo != user.Id) && lead.SecondaryUserId != user.Id)
                            {
                                #region UpdatingPickedDate

                                lead.ShouldUpdatePickedDate = false;
                                lead.PickedDate = null;
                                lead.IsPicked = false;

                                #endregion
                                var previousAssignedFrom = lead.AssignedFrom;
                                lead.AssignedFrom =  lead.AssignTo;
                                lead.AssignTo = user.Id;

                                groupedAssignedLeads[lead.AssignTo] = new List<Domain.Entities.Lead>();

                                groupedAssignedLeads[lead.AssignTo].Add(lead);

                                await SetReassignedLeadEnquiryAsync(lead, assignmentDto, cancellationToken);

                                if (assignmentDto.UpdateProject && assignmentDto.Projects != null && assignmentDto.Projects.Any())
                                {
                                    await SetLeadProjectsAsync(lead, assignmentDto.Projects, globalSettings, cancellationToken);
                                }

                                await SetReassignedLeadDetailsAsync(lead, assignmentDto, cancellationToken, newStatus);

                                await _leadRepo.UpdateAsync(lead, cancellationToken);

                                await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, userDetails, cancellationToken, currentUserId,previousAssignedFrom:previousAssignedFrom);
                                //users = users.Where(i => i.Id != currentUserId).ToList();
                                i = i == users.Count - 1 ? 0 : i + 1;
                            }
                            else
                            {
                                var existingSkippedLead = skippedLeadsDtos.FirstOrDefault(x => x.User?.Id == user.Id);
                                if (existingSkippedLead != null)
                                {
                                    existingSkippedLead.Leads?.Add(lead.Adapt<DuplicateAssigmentLeadDto>());
                                }
                                else
                                {
                                    var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                                    {
                                        User = new()
                                        {
                                            Id = user.Id,
                                            Name = user.FirstName + " " + user.LastName
                                        },
                                        Leads = new List<DuplicateAssigmentLeadDto> { lead.Adapt<DuplicateAssigmentLeadDto>() }
                                    };
                                    skippedLeadsDtos.Add(skippedLeadsDto);
                                }
                            }
                        }
                        foreach (var group in groupedAssignedLeads)
                        {
                            await SendLeadAssignmentNotificationsAsync(group.Value[0], group.Value.Count, globalSettings, cancellationToken);
                        }

                        return (leads, skippedLeadsDtos);
                    }
                    else
                    {
                        throw new NotFoundException("No users found by the provided user ids.");
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<List<Domain.Entities.Lead>> MakeLeadsAsUnAssignedAsync(List<Domain.Entities.Lead> leads, CancellationToken cancellationToken, Guid? currentUserId = null)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            leads = leads.Where(i => i.AssignTo != Guid.Empty).ToList();

            #region UpdatingPickedDate
            leads.ForEach(lead =>
            {
                lead.ShouldUpdatePickedDate = false;
                lead.PickedDate = null;
                lead.IsPicked = false;
            });
            #endregion

            foreach (var lead in leads)
            {
                lead.AssignedFrom = lead.AssignTo;
                lead.AssignTo = Guid.Empty;
                lead.SecondaryUserId = Guid.Empty;
                await _leadRepo.UpdateAsync(lead, cancellationToken);
                await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken, currentUserId: currentUserId);
            }
            if (leads.Any())
            {
                await SendLeadAssignmentNotificationsAsync(leads[0], leads.Count, globalSettings, cancellationToken);
            }
            return leads;
        }
        protected async Task SetReassignedLeadDetailsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken, CustomMasterLeadStatus? newStatus = null)
        {
            try
            {
                switch (assignmentDto.AssignmentType)
                {
                    case LeadAssignmentType.WithoutHistory:
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        lead.Notes = null;
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistory, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithoutHistoryWithNewStatus:
                        await InitializeLeadStatusAsync(lead, cancellationToken, newStatus);
                        lead.ScheduledDate = null;
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        lead.Notes = null;
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistoryWithNewStatus, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithHistory:
                        // await UpdateLeadAppointmentAndCommunicationAsync(lead, cancellationToken);
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory, assignmentDto, cancellationToken);
                        break;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(string ContactNo, string? AltContactNo)> ValidateContactNoAsync(string contactNo, string? alternateContactNo, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                //var globalSettingInfo = await _globalsettingRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var hasInternationalSupportEnabled = globalSettings?.HasInternationalSupport ?? false;
                if (contactNo.Length == 10 && !hasInternationalSupportEnabled)
                {
                    contactNo = $"+91{contactNo.Trim()}";
                }
                bool IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(contactNo, hasInternationalSupportEnabled);
                if (!IsValidContactNo) { throw new Exception("Invalid ContactNo"); }
                if (alternateContactNo != null)
                {
                    if (alternateContactNo.Length == 10 && !hasInternationalSupportEnabled)
                    {
                        alternateContactNo = $"+91{alternateContactNo}";
                    }
                    IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(alternateContactNo, hasInternationalSupportEnabled);
                    if (!IsValidContactNo) { throw new Exception("Invalid AltNumber"); }
                }
                return (contactNo, alternateContactNo);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        //protected async Task<MasterPropertyType?> ValidatePropertyTypeAsync(Guid? propertyTypeId, CancellationToken cancellationToken = default)
        //{
        //    try
        //    {
        //        MasterPropertyType? propertyType = null;
        //        if (propertyTypeId != Guid.Empty && propertyTypeId != null)
        //        {
        //            propertyType = await _propertyTypeRepo.GetByIdAsync(propertyTypeId ?? Guid.Empty, cancellationToken);
        //            if (propertyType == null)
        //            {
        //                throw new InvalidDataException("Property type Id does not belong to master data");
        //            }
        //        }
        //        return propertyType;
        //    }
        //    catch (Exception ex)
        //    {
        //        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
        //        throw;
        //    }
        //}
        protected async Task<List<MasterPropertyType?>> ValidatePropertyTypesAsync(List<Guid> propertyTypeIds, CancellationToken cancellationToken = default)
        {
            try
            {
                List<MasterPropertyType>? propertyType = null;

                if (propertyTypeIds != null && propertyTypeIds.Any())
                {
                    propertyType = await _propertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(propertyTypeIds), cancellationToken);
                }

                return propertyType;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadEnquiryAsync(Domain.Entities.Lead lead, CreateLeadEnquiryDto? enquiryDto, Address? address = null,MasterPropertyType? propertyType = null, List<Address>? addresses = null, Guid? templateId = null, CancellationToken cancellationToken = default, List<MasterPropertyType>? propertyTypes = null,Domain.Entities.GlobalSettings? globalsettings=null)
        {
            try
            {
                var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty));
                if (template != null)
                {
                    Guid defaultUnitId = Guid.TryParse(globalsettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : Guid.Empty;
                    var enquiry = enquiryDto?.Adapt<LeadEnquiry>();
                    enquiry ??= new();
                    enquiry.IsPrimary = true;
                    enquiry.Address = address;
                    enquiry.PropertyType = propertyTypes?.FirstOrDefault();
                    enquiry.CarpetAreaInSqMtr = ((enquiryDto?.CarpetArea ?? 0) * (enquiryDto?.ConversionFactor ?? 0));
                    enquiry.BuiltUpAreaInSqMtr = ((enquiryDto?.BuiltUpArea ?? 0) * (enquiryDto?.BuiltUpAreaConversionFactor ?? 0));
                    enquiry.SaleableAreaInSqMtr = ((enquiryDto?.SaleableArea ?? 0) * (enquiryDto?.SaleableAreaConversionFactor ?? 0));
                    enquiry.Addresses = addresses;
                    enquiry.PropertyTypes = propertyTypes;
                    enquiry.LeadSource = template.LeadSource ?? LeadSource.QRCode;
                    enquiry.SubSource = (template.SubSource + $"-{template.Name}").ToLower();
                    enquiry.CarpetAreaUnitId = (enquiryDto?.CarpetArea != null && enquiryDto?.CarpetArea != default && enquiryDto?.CarpetArea != 0) ? enquiryDto?.CarpetAreaUnitId ?? defaultUnitId : enquiry.CarpetAreaUnitId;
                    enquiry.SaleableAreaUnitId = (enquiryDto?.SaleableArea != null && enquiryDto?.SaleableArea != default && enquiryDto?.SaleableArea != 0) ? enquiryDto?.SaleableAreaUnitId ?? defaultUnitId : enquiry.SaleableAreaUnitId;
                    enquiry.BuiltUpAreaUnitId = (enquiryDto?.BuiltUpArea != null && enquiryDto?.BuiltUpArea != default && enquiryDto?.BuiltUpArea != 0) ? enquiryDto?.BuiltUpAreaUnitId ?? defaultUnitId : enquiry.BuiltUpAreaUnitId;
                    enquiry.NetAreaUnitId = (enquiryDto?.NetArea != null && enquiryDto?.NetArea != default && enquiryDto?.NetArea != 0) ? enquiryDto?.NetAreaUnitId ?? defaultUnitId : enquiry.NetAreaUnitId;
                    enquiry.PropertyAreaUnitId = (enquiryDto?.PropertyArea != null && enquiryDto?.PropertyArea != default && enquiryDto?.PropertyArea != 0) ? enquiryDto?.PropertyAreaUnitId ?? defaultUnitId : enquiry.PropertyAreaUnitId;
                    lead.Enquiries ??= new List<LeadEnquiry>();
                    lead.Enquiries.Add(enquiry);
                }
                else
                {
                    var enquiry = enquiryDto?.Adapt<LeadEnquiry>();
                    enquiry ??= new();
                    enquiry.IsPrimary = true;
                    enquiry.Address = address;
                    enquiry.PropertyType = propertyTypes?.FirstOrDefault();
                    enquiry.CarpetAreaInSqMtr = ((enquiryDto?.CarpetArea ?? 0) * (enquiryDto?.ConversionFactor ?? 0));
                    enquiry.BuiltUpAreaInSqMtr = ((enquiryDto?.BuiltUpArea ?? 0) * (enquiryDto?.BuiltUpAreaConversionFactor ?? 0));
                    enquiry.SaleableAreaInSqMtr = ((enquiryDto?.SaleableArea ?? 0) * (enquiryDto?.SaleableAreaConversionFactor ?? 0));
                    enquiry.NetAreaInSqMtr = ((enquiryDto?.NetArea ?? 0) * (enquiryDto?.NetAreaConversionFactor ?? 0));
                    enquiry.PropertyAreaInSqMtr = ((enquiryDto?.PropertyArea ?? 0) * (enquiryDto?.PropertyAreaConversionFactor ?? 0));
                    enquiry.Addresses = addresses;
                    enquiry.PropertyTypes = propertyTypes;
                    lead.Enquiries ??= new List<LeadEnquiry>();
                    lead.Enquiries.Add(enquiry);

                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadAssignedToAsync(Domain.Entities.Lead lead, Guid? userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var currentUserId = _currentUserRepo.GetUserId();

                if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                {
                    lead.AssignedFrom = lead.AssignTo;
                    lead.AssignTo = currentUserId;
                }
                else
                {
                    //lead.AssignedFrom = lead.AssignTo;
                    lead.AssignTo = userId ?? Guid.Empty;
                }
                if (lead.AssignTo == Guid.Empty)
                {
                    lead.SecondaryUserId = Guid.Empty;
                }
                if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                {
                    lead.OriginalOwner = lead.AssignTo;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task SetLeadAssignedToAsync(Guid? templateId, Guid? projectId, Guid? locationId, Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead?.Enquiries?.FirstOrDefault()?.LeadSource != LeadSource.QRCode)
                {
                    var currentUserId = _currentUserRepo.GetUserId();
                    if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                    {
                        //lead.AssignedFrom = currentUserId;
                        lead.AssignTo = currentUserId;
                        lead.OriginalOwner = lead.AssignTo;
                    }
                    if (lead.AssignTo == Guid.Empty)
                    {
                        lead.SecondaryUserId = Guid.Empty;
                    }
                }
                else if (lead?.Enquiries?.FirstOrDefault()?.LeadSource != null)
                {
                    var project = await _projectRepo.FirstOrDefaultAsync(new ProjectByIdSpec(projectId ?? Guid.Empty));
                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationId ?? Guid.Empty));

                    (UserAssignment? UserAssignment, Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                    //Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                    userAssignmentAndProject = await Automation.Helpers.QRAssignmentHelper.GetMostPrioritizedUserAssignmentAsync(templateId, lead.Enquiries.FirstOrDefault().LeadSource, _qrAssignmentRepo, _assignmentModuleRepo, globalSettings, _qrFormTemplateRepo, projectWithAssignment: project, locationWithUserAssignment: location);
                    var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                    if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default)
                    {
                        lead.AssignTo = existingLead.AssignTo;
                    }
                    else
                    {
                        List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo?[^10..] ?? "Invalid Number" })) ?? new();
                        (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                        var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();

                        if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                        {
                            bool isAssigned = true;
                            while (isAssigned)
                            {
                                userAssignmentAndProject = await QRAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(templateId, lead.Enquiries.FirstOrDefault().LeadSource, _qrAssignmentRepo, _assignmentModuleRepo, globalSettings, _qrFormTemplateRepo, projectWithAssignment: project, locationWithUserAssignment: location, priority: userAssignmentAndProject.Priority);

                                assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                if (assignToRes.AssignTo != Guid.Empty)
                                {
                                    isAssigned = false;
                                }
                                else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                {
                                    userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                }
                                else
                                {
                                    isAssigned = false;
                                }
                            }

                        }

                        lead.AssignTo = assignToRes.AssignTo;
                        if (lead.AssignTo == Guid.Empty)
                        {
                            lead.AssignTo = _currentUserRepo.GetUserId();
                        }
                        if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                        {
                            lead.OriginalOwner = lead.AssignTo;
                        }
                        _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                    }

                    if (project != null)
                    {
                        List<Domain.Entities.Project>? projects = new();
                        projects.Add(project);
                        lead.Projects = projects;
                    }

                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadProjectsAsync(Domain.Entities.Lead lead, List<string>? projectList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

                List<Lrb.Domain.Entities.Project>? projects = new();
                //Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken)).FirstOrDefault();
                        if (existingProject != null)
                        {
                            projects.Add(existingProject);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Project project = new()
                            {
                                Name = newProject,
                                MonetaryInfo = new ProjectMonetaryInfo
                                {
                                    Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                }
                            };
                            project = await _projectRepo.AddAsync(project, cancellationToken);
                            projects.Add(project);
                        }
                    }
                    lead.Projects = projects;
                }
                else if ((lead?.Projects?.Any() ?? false) && projectList == null)
                {
                    lead.Projects = projects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadAgencyAsync(Domain.Entities.Lead lead, List<string>? agencyList, string? agencyName = null, Guid? templateId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Agencies = null;
                var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty));

                if (!string.IsNullOrWhiteSpace(template?.Agency?.Name ?? string.Empty))
                {
                    if (agencyList?.Any() ?? false)
                    {
                        agencyList?.Add(template?.Agency?.Name);
                    }
                    else
                    {
                        agencyList = new() { template?.Agency?.Name };
                    }
                }

                //if (!string.IsNullOrWhiteSpace(agencyName))
                //{
                //    if (agencyList?.Any() ?? false)
                //    {
                //        agencyList?.Add(agencyName);
                //    }
                //    else
                //    {
                //        agencyList = new() { agencyName };
                //    }
                //}

                List<Lrb.Domain.Entities.Agency>? agencies = new();
                agencyList = (agencyList?.Any() ?? false) ? agencyList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                if (agencyList?.Any() ?? false)
                {
                    foreach (var newAgency in agencyList)
                    {
                        Lrb.Domain.Entities.Agency? existingAgency = (await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(newAgency), cancellationToken));
                        if (existingAgency != null)
                        {
                            agencies.Add(existingAgency);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Agency agency = new() { Name = newAgency };
                            agency = await _agencyRepo.AddAsync(agency, cancellationToken);
                            agencies.Add(agency);
                        }
                    }
                    //lead.Agencies = agencies;
                    lead.Agencies = (agencies?.Any() ?? false) ? agencies.Where(i => !string.IsNullOrWhiteSpace(i.Name)).Distinct().ToList() : null;

                }
                else if ((lead?.Agencies?.Any() ?? false) && agencyList == null)
                {
                    lead.Agencies = agencies;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadCampaignAsync(Domain.Entities.Lead lead, List<string>? campaignList, Guid? templateId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Campaigns = null;
                var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty));

                if (!string.IsNullOrWhiteSpace(template?.Campaign?.Name ?? string.Empty))
                {
                    if (campaignList?.Any() ?? false)
                    {
                        campaignList?.Add(template.Campaign.Name);
                    }
                    else
                    {
                        campaignList = new() { template.Campaign.Name };
                    }
                }
                List<Lrb.Domain.Entities.Campaign>? campaigns = new();
                campaignList = (campaignList?.Any() ?? false) ? campaignList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                if (campaignList?.Any() ?? false)
                {
                    foreach (var newCampaign in campaignList)
                    {
                        Lrb.Domain.Entities.Campaign? existingCampaign = (await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpec(newCampaign), cancellationToken));
                        if (existingCampaign != null)
                        {
                            campaigns.Add(existingCampaign);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Campaign campaign = new() { Name = newCampaign };
                            campaign = await _campaignRepo.AddAsync(campaign, cancellationToken);
                            campaigns.Add(campaign);
                        }
                    }
                    lead.Campaigns = campaigns;
                }
                else if ((lead?.Campaigns?.Any() ?? false) && campaignList == null)
                {
                    lead.Campaigns = campaigns;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateLeadCustomFlagsByIdAsync(Domain.Entities.Lead lead, Guid? templateId, CancellationToken cancellationToken = default)
        {
            var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty));
            List<Guid>? flags = null;
            try
            {
                if (template != null)
                {
                    flags = template.Campaign?.Flags?.Select(c => c.Id).ToList();
                }
                var results = new List<string>();
                var userId = _currentUser.GetUserId();
                if (flags != null)
                {
                    foreach (var flag in flags)
                    {
                        if (lead.CustomFlags?.Any(i => i.FlagId == flag) ?? false)
                        {
                            var leadflag = lead.CustomFlags?.FirstOrDefault(i => i.FlagId == flag);
                            if (leadflag?.IsSelected ?? false)
                            {
                                lead.CustomFlags?.Remove(leadflag);
                            }
                        }
                        else
                        {
                            var flagToadd = await _flagRepository.FirstOrDefaultAsync(new GetCampaignFlagsByIdSpec(flag));
                            var customFlag = new CustomFlag() { Lead = lead, LeadId = lead.Id, Flag = flagToadd, FlagId = flag, IsSelected = true, UserId = userId };

                            if (lead.CustomFlags != null)
                            {
                                lead.CustomFlags.Add(customFlag);
                            }
                            else
                            {
                                lead.CustomFlags = new List<CustomFlag>() { customFlag };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadPropertiesAsync(Domain.Entities.Lead lead, List<string>? propertyList, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

                List<Domain.Entities.Property>? properties = new();
                //Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                propertyList = (propertyList?.Any() ?? false) ? propertyList.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList()?.ConvertAll(i => i.ToLower().Trim()) : null;
                if (propertyList?.Any() ?? false)
                {
                    foreach (var newProperty in propertyList)
                    {
                        var existingProperty = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken);
                        if (existingProperty != null)
                        {
                            properties.Add(existingProperty);
                        }
                        else
                        {
                            Domain.Entities.Property property = new()
                            {
                                Title = newProperty,
                                MonetaryInfo = new PropertyMonetaryInfo
                                {
                                    Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                }
                            };
                            property = await _propertyRepo.AddAsync(property, cancellationToken); ;
                            properties.Add(property);
                        }
                    }
                    lead.Properties = properties;
                }
                else if ((lead?.Properties?.Any() ?? false) && propertyList == null)
                {
                    lead.Properties = properties;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetChannelPartnersAsync(Domain.Entities.Lead lead, List<string>? channelPartnersList, Guid? templateId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.ChannelPartners = null;
                var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty));

                if (!string.IsNullOrWhiteSpace(template?.ChannelPartner?.FirmName ?? string.Empty))
                {
                    if (channelPartnersList?.Any() ?? false)
                    {
                        channelPartnersList?.Add(template.ChannelPartner.FirmName);
                    }
                    else
                    {
                        channelPartnersList = new() { template.ChannelPartner.FirmName };
                    }
                }
                List<Lrb.Domain.Entities.ChannelPartner>? channelPartners = new();
                channelPartnersList = (channelPartnersList?.Any() ?? false) ? channelPartnersList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                if (channelPartnersList?.Any() ?? false)
                {
                    foreach (var newCp in channelPartnersList)
                    {
                        Lrb.Domain.Entities.ChannelPartner? existingCp = (await _cpRepository.FirstOrDefaultAsync(new GetChannelPartnerByNameSpecs(newCp), cancellationToken));
                        if (existingCp != null)
                        {
                            channelPartners.Add(existingCp);
                        }
                        else
                        {
                            Lrb.Domain.Entities.ChannelPartner channelPartner = new() { FirmName = newCp };
                            channelPartner = await _cpRepository.AddAsync(channelPartner, cancellationToken);
                            channelPartners.Add(channelPartner);
                        }
                    }
                    lead.ChannelPartners = channelPartners;
                }
                else if ((lead?.ChannelPartners?.Any() ?? false) && channelPartnersList == null)
                {
                    lead.ChannelPartners = channelPartners;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadAppointmentAsync(Domain.Entities.Lead lead, LeadAppointment appointment, AppointmentType appointmentType, List<LeadDocument>? documentsToAdd = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var address = appointment.Location != null ? await CreateAddressAsync(appointment.Location.Adapt<AddressDto>(), cancellationToken) : null;


                if (appointmentType != AppointmentType.None)
                {
                    appointment.Id = Guid.Empty;
                    //appointment.UserId = lead.AssignTo;
                    switch (appointmentType)
                    {
                        case AppointmentType.Meeting:
                            lead.IsMeetingDone = appointment.IsDone;
                            appointment.Type = AppointmentType.Meeting;
                            break;
                        case AppointmentType.SiteVisit:
                            lead.IsSiteVisitDone = appointment.IsDone;
                            appointment.Type = AppointmentType.SiteVisit;
                            break;
                        default:
                            break;
                    }
                    if (address != null)
                    {
                        lead.MeetingLocation = address.Id;
                        appointment.Location = address;
                    }
                    if (documentsToAdd != null && documentsToAdd.Any())
                    {
                        var docType = appointmentType == AppointmentType.Meeting ? Domain.Enums.LeadDocumentType.Meeting : appointmentType == AppointmentType.SiteVisit ? Domain.Enums.LeadDocumentType.SiteVisit : Domain.Enums.LeadDocumentType.None;
                        var documents = await CreateTrueLeadDocsAsync(documentsToAdd, docType);
                        appointment.ImagesWithName = documents;
                    }
                    if (lead.Appointments?.Any() ?? false)
                    {
                        lead.Appointments.Add(appointment);
                    }
                    else
                    {
                        lead.Appointments = new List<LeadAppointment>() { appointment };
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetBookedDetailsAsync(Domain.Entities.Lead lead, UpdateLeadStatusRequest request, CancellationToken cancellationToken = default)
        {
            var userName = await _userService.GetAsync(lead.BookedBy.ToString() ?? string.Empty, cancellationToken);
            var bookedDetailInfo = await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(lead.Id), cancellationToken);
            if (bookedDetailInfo != null)
            {
                try
                {
                    bookedDetailInfo.BookedDate = lead?.BookedDate;
                    bookedDetailInfo.BookedBy = lead?.BookedBy;
                    bookedDetailInfo.BookedByUser = userName?.FirstName ?? string.Empty + " " + userName?.LastName ?? string.Empty;
                    bookedDetailInfo.BookedUnderName = lead?.BookedUnderName;
                    bookedDetailInfo.UserId = lead?.AssignTo ?? Guid.Empty;
                    bookedDetailInfo.SoldPrice = request?.SoldPrice;
                    bookedDetailInfo.Notes = request?.Notes;
                    bookedDetailInfo.ProjectsList = request?.ProjectsList;
                    bookedDetailInfo.PropertiesList = request?.PropertiesList;
                    bookedDetailInfo.AgreementValue = request?.AgreementValue ?? default;
                    bookedDetailInfo.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                    bookedDetailInfo.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                    bookedDetailInfo.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                    bookedDetailInfo.Currency = request?.Currency ?? default;
                    bookedDetailInfo.UnitType = await _unitType.GetBySpecAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                    bookedDetailInfo.IsBookingCompleted = request?.IsBookingCompleted ?? bookedDetailInfo.IsBookingCompleted;
                    await _leadBookedDetailRepo.UpdateAsync(bookedDetailInfo);
                    if (request?.AssignTo != null)
                    {
                        lead.AssignTo = request?.AssignTo ?? lead.AssignTo;
                        await _leadRepo.UpdateAsync(lead);
                    }
                }
                catch (Exception ex)
                {
                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                    throw;
                }

            }
            else
            {
                if (lead != null)
                {
                    try
                    {
                        LeadBookedDetail bookedDetail = new();
                        bookedDetail.LeadId = lead.Id;
                        bookedDetail.BookedDate = lead?.BookedDate;
                        bookedDetail.BookedBy = lead?.BookedBy;
                        bookedDetail.BookedByUser = userName?.FirstName ?? string.Empty + " " + userName?.LastName ?? string.Empty;
                        bookedDetail.BookedUnderName = lead?.BookedUnderName;
                        bookedDetail.UserId = lead?.AssignTo ?? Guid.Empty;
                        bookedDetail.SoldPrice = request?.SoldPrice;
                        bookedDetail.Notes = request?.Notes;
                        bookedDetail.ProjectsList = request?.ProjectsList;
                        bookedDetail.PropertiesList = request?.PropertiesList;
                        bookedDetail.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetail.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetail.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        bookedDetail.Currency = request?.Currency ?? default;
                        bookedDetail.UnitType = await _unitType.GetBySpecAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                        bookedDetail.IsBookingCompleted = request?.IsBookingCompleted ?? false;
                        lead?.BookedDetails?.Add(bookedDetail);
                        await _leadBookedDetailRepo.AddAsync(bookedDetail);
                        if (request?.AssignTo != null)
                        {
                            lead.AssignTo = request?.AssignTo ?? lead.AssignTo;
                            await _leadRepo.UpdateAsync(lead);
                        }

                    }


                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        throw;
                    }
                }
            }
        }

        protected async Task SetLeadDocsAsync(Domain.Entities.Lead lead, List<LeadDocument>? documentsToAdd, Domain.Enums.LeadDocumentType documentType, CancellationToken cancellationToken = default)
        {
            try
            {
                if (documentsToAdd != null && documentsToAdd.Any())
                {
                    if (documentsToAdd != null)
                    {
                        var documents = await CreateTrueLeadDocsAsync(documentsToAdd, documentType);
                        if (lead.Documents != null)
                        {
                            lead.Documents.AddRange(documents);
                        }
                        else
                        {
                            lead.Documents = documents;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadNameAndNumberAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead != null)
                {
                    string name = string.IsNullOrWhiteSpace(lead.Name?.Trim()) ? "Unknwon" : lead.Name.Trim();
                    lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetLeadStatusAsync(Domain.Entities.Lead lead, Guid statusId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (statusId == default)
                {
                    throw new ArgumentException("The Status Id is not valid");
                }
                var chaildStatuses = await _customLeadStatusRepo.ListAsync(new LeadStatusSpec(statusId), cancellationToken);
                if (chaildStatuses?.Any() ?? false)
                {
                    throw new ArgumentException("Please provide child status id.");
                }
                var leadStatus = await _customLeadStatusRepo.GetByIdAsync(statusId, cancellationToken);

                if (leadStatus == null)
                {
                    throw new ArgumentException("The Status Id is not valid.");
                }
                lead.CustomLeadStatus = leadStatus;
                lead.CustomLeadStatusId = leadStatus.Id;
                //lead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(leadStatus.MasterLeadStatusBaseId ?? Guid.Empty), cancellationToken);
                if (leadStatus != null && leadStatus.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                {
                    lead.ScheduledDate = null;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateDuplicateVersionAsync(Domain.Entities.Lead lead, Guid? parentLeadId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var rootLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo, lead.Id), cancellationToken);
                if (rootLead != null)
                {
                    lead.RootId = rootLead.Id;
                    lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                    lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                    rootLead.ChildLeadsCount += 1;
                    try
                    {
                        await _leadRepo.UpdateAsync(rootLead);
                    }
                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateProjectsForMultipleLeadsAsync(List<Domain.Entities.Lead> leads, List<Lrb.Domain.Entities.Project> projects, bool ShouldRemoveExistingProjects, CancellationToken cancellationToken = default, Guid? userId = null)
        {
            try
            {
                var allLeads = new List<Domain.Entities.Lead>();
                var currentUserId = userId ?? _currentUser.GetUserId();
                foreach (var lead in leads)
                {
                    try
                    {
                        lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, new() { Projects = projects.Adapt<List<ProjectsDtoV2>>() });
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                    if ((lead.Projects?.Any() ?? false) && !ShouldRemoveExistingProjects)
                    {
                        List<Lrb.Domain.Entities.Project> newProjects = new();
                        newProjects.AddRange(lead.Projects);
                        newProjects.AddRange(projects);
                        newProjects = newProjects.DistinctBy(p => p.Id).ToList();
                        lead.Projects = newProjects;
                    }
                    else
                    {
                        lead.Projects = projects;
                    }
                    lead.LastModifiedOn = DateTime.UtcNow;
                    lead.LastModifiedBy = currentUserId;
                    allLeads.Add(lead);

                }
                if (allLeads?.Any() ?? false)
                {
                    await _leadRepo.UpdateRangeAsync(allLeads, cancellationToken);

                    allLeads = await _leadRepo.ListAsync(new LeadByIdSpec(allLeads?.Select(i => i.Id).ToList()), cancellationToken);
                    List<ViewLeadDto> leadDtos = new List<ViewLeadDto>();
                    List<Domain.Entities.LeadHistory>? existingLeadHistories = new();
                    List<Domain.Entities.LeadHistory>? newLeadHistories = new();
                    foreach (var oldlead in allLeads)
                    {
                        var leadDto = await GetFullLeadDtoAsync(oldlead, cancellationToken, currentUserId: currentUserId);
                        leadDtos.Add(leadDto);
                        var leadHistories = await UpdateLeadHistoryAsyncV2(oldlead, leadDto: leadDto, null, cancellationToken: cancellationToken, null, currentUserId: null);
                        if (leadHistories.Item1 != null)
                        {
                            existingLeadHistories.Add(leadHistories.Item1);
                        }
                        if (leadHistories.Item2 != null)
                        {
                            newLeadHistories.Add(leadHistories.Item2);
                        }
                    }
                    if (existingLeadHistories?.Any() ?? false)
                    {
                        await _leadHistoryRepo.UpdateRangeAsync(existingLeadHistories, cancellationToken);
                    }
                    if (newLeadHistories?.Any() ?? false)
                    {
                        await _leadHistoryRepo.AddRangeAsync(newLeadHistories, cancellationToken);
                    }
                }

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateLeadEnquiryAsync(Domain.Entities.Lead lead, CreateLeadEnquiryDto? enquiryDto, Address? address, List<Address>? addresses, MasterPropertyType? propertyType = null, CancellationToken cancellationToken = default, List<MasterPropertyType>? propertyTypes = null)
        {
            try
            {
                LeadEnquiry leadEnquiry = enquiryDto?.Adapt<LeadEnquiry>() ?? new();
                leadEnquiry.Address = address;
                leadEnquiry.Addresses = addresses;
                leadEnquiry.PropertyType = propertyType;
                leadEnquiry.PropertyTypes = propertyTypes;
                leadEnquiry.IsPrimary = true;
                if (enquiryDto?.CarpetArea != null)
                {
                    leadEnquiry.CarpetAreaInSqMtr = (enquiryDto.CarpetArea * enquiryDto.ConversionFactor);
                }
                if (enquiryDto?.BuiltUpArea != null)
                {
                    leadEnquiry.BuiltUpAreaInSqMtr = (enquiryDto.BuiltUpArea * enquiryDto.BuiltUpAreaConversionFactor);
                }
                if (enquiryDto?.SaleableArea != null)
                {
                    leadEnquiry.SaleableAreaInSqMtr = (enquiryDto.SaleableArea * enquiryDto.SaleableAreaConversionFactor);
                }
                //await UpdateLeadSourceInfoAsync(leadEnquiry, cancellationToken);
                var existingEnquiry = lead.Enquiries.Count > 0 ? lead.Enquiries[0] : default;
                if (existingEnquiry is not null)
                {
                    var propertyTypeIds = new List<Guid>()
                {
                    Guid.Parse("7d583139-e87d-4d2e-9d04-3bffebf82942"),
                    Guid.Parse("8ba96762-16f2-4735-bc5a-138573081a19"),
                    Guid.Parse("8d178b31-8008-4e19-8eeb-5189f7578044"),

                };
                    if (existingEnquiry.PropertyType != null && propertyTypeIds.Contains(existingEnquiry.PropertyType.Id) && !propertyTypeIds.Contains(leadEnquiry.PropertyType?.Id ?? Guid.Empty))
                    {
                        //leadEnquiry.NoOfBHKs = 0;
                        //leadEnquiry.BHKType = BHKType.None;
                        leadEnquiry.BHKs = new List<double>();
                        leadEnquiry.BHKTypes = new List<BHKType>();
                    }
                    if (existingEnquiry.PropertyTypes != null && existingEnquiry.PropertyTypes.Select(i => i.Id).Any(id => propertyTypeIds.Contains(id)) && leadEnquiry.PropertyTypes != null && !leadEnquiry.PropertyTypes.Select(i => i.Id).Any(id => propertyTypeIds.Contains(id)))
                    {
                        leadEnquiry.BHKs = new List<double>();
                        leadEnquiry.BHKTypes = new List<BHKType>();
                    }

                    existingEnquiry.Update(leadEnquiry);
                    await _leadEnquiryRepo.UpdateAsync(existingEnquiry, cancellationToken);
                }
                else
                {
                    leadEnquiry.LeadId = lead.Id;
                    await _leadEnquiryRepo.AddAsync(leadEnquiry);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SetReassignedLeadEnquiryAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.Enquiries != null && lead.Enquiries.Any())
                {
                    var primaryEnquiry = lead.Enquiries.FirstOrDefault(e => e.IsPrimary);
                    if (primaryEnquiry != null)
                    {
                        if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                        {
                            primaryEnquiry.SubSource = assignmentDto.SubSource;
                        }
                        if (assignmentDto.UpdateSource)
                        {
                            primaryEnquiry.LeadSource = assignmentDto.LeadSource;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task UpdateLeadSourceInfoAsync(LeadEnquiry leadEnquiry, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!string.IsNullOrEmpty(leadEnquiry.SubSource) &&
                leadEnquiry.LeadSource != LeadSource.Facebook &&
                leadEnquiry.LeadSource != LeadSource.GoogleAds &&
                leadEnquiry.LeadSource != LeadSource.Gmail)
                {
                    var sourceDtos = (await _dapperRepository.GetAllIntegrationSubSourceAsync<SourceDto>(_currentUser.GetTenant() ?? string.Empty)).Where(i => i.SubSource != null &&
                    i.LeadSource != LeadSource.Facebook && i.LeadSource != LeadSource.GoogleAds && i.LeadSource != LeadSource.Gmail && i.LeadSource == leadEnquiry.LeadSource).ToList();
                    if (!sourceDtos.Any(i => i.SubSource?.ToLower()?.Trim()?.Contains(leadEnquiry.SubSource?.ToLower()?.Trim() ?? "Invalid") ?? false))
                    {
                        try
                        {
                            await _mediator.Send(new CreateIntegrationRequest()
                            {
                                AccountName = leadEnquiry.SubSource,
                                Source = leadEnquiry.LeadSource
                            });
                        }
                        catch (Exception ex)
                        {
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateLeadContactRecordsCountAsync(Domain.Entities.Lead lead, ContactType contactType, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null)
        {
            try
            {
                if (lead.ContactRecords != null && lead.ContactRecords.ContainsKey(contactType))
                {
                    lead.ContactRecords[contactType] += 1;
                }
                else if (lead.ContactRecords == null)
                {
                    lead.ContactRecords = new Dictionary<ContactType, int>() { { contactType, 1 } };
                }
                else
                {
                    lead.ContactRecords[contactType] = 1;
                }
                await _leadRepo.UpdateAsync(lead, cancellationToken);
                var leadDto = lead.Adapt<ViewLeadDto>();
                leadDto.ContactRecords = leadDto.ContactRecords?.Where(i => i.Key == contactType).ToDictionary(i => i.Key, i => i.Value);
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken, shouldUpdateContactRecord: shouldUpdateContactRecord);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(Domain.Entities.LeadHistory, Domain.Entities.LeadHistory)> UpdateLeadHistoryAsyncV2(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            Domain.Entities.LeadHistory? existingHistory = null;
            Domain.Entities.LeadHistory? newHistory = null;
            try
            {
                var userId = currentUserId ?? _currentUserRepo.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId: currentUserId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                    }
                    else
                    {
                        newHistory = leadHistory;
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory);
                            existingHistory = history;
                        }

                        else
                        {
                            newHistory = leadHistory;
                        }
                    }
                }
                return new(existingHistory, newHistory);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false, Guid? previousAssignedFrom = null)
        {
            try
            {
                var userId = currentUserId ??= _currentUser.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId);

                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = previousAssignedFrom ?? lead.AssignedFrom ?? userId;
                    var existingLeadHistory = (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId))) ?? (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedFrom ?? lead.AssignedFrom ?? Guid.Empty)));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new GetParentLeadHistorySpec(lead.Id));
                        if (existingLeadHistory != null)
                        {
                            existingLeadHistory.UserId = currentUserId ?? _currentUser.GetUserId();
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ?? (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedFrom ?? userId)));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new GetParentLeadHistorySpec(lead.Id));
                            if (existingLeadHistory != null)
                            {
                                existingLeadHistory.UserId = currentUserId ?? _currentUser.GetUserId();
                                await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                            }
                            else
                            {
                                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateLeadHistoryV2Async(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null)
        {
            try
            {
                var data = new Lrb.Application.Common.ServiceBus.LeadHistoryDto() { Lead = lead, AppointmentType = appointmentType, LeadDto = leadDto, ShouldUpdateContactRecord = shouldUpdateContactRecord };
                var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "leadhistory", data);
                await _serviceBus.RunLeadHistoryJobAsync(payload);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task<string> UpdateLeadTagsByEnumAsync(Domain.Entities.Lead lead, LeadTagEnum? leadTags, CancellationToken cancellationToken = default)
        {
            try
            {
                LeadTag? leadTag = lead.TagInfo ?? null;
                string message;
                if (leadTag is not null)
                {
                    message = SetLeadTags(leadTag, leadTags);
                    await _leadTagRepo.UpdateAsync(leadTag, cancellationToken);
                }
                else
                {
                    LeadTag newLeadTag = new()
                    {
                        LeadId = lead.Id
                    };
                    message = SetLeadTags(newLeadTag, leadTags);
                    await _leadTagRepo.AddAsync(newLeadTag, cancellationToken);
                }
                return message;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<(Domain.Entities.Lead, string)> UpdateLeadCustomFlagsByIdAsync(Domain.Entities.Lead lead, Flag flag, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = string.Empty;
                var userId = _currentUser.GetUserId();
                if (lead.CustomFlags?.Any(i => i.FlagId == flag.Id) ?? false)
                {
                    var leadflag = lead.CustomFlags?.FirstOrDefault(i => i.FlagId == flag.Id);
                    if (leadflag?.IsSelected ?? false)
                    {
                        lead.CustomFlags?.Remove(leadflag);
                    }
                    result = "UnTagged";
                }
                else
                {
                    var customFlag = new CustomFlag() { Lead = lead, LeadId = lead.Id, Flag = flag, FlagId = flag.Id, IsSelected = true };
                    customFlag.UserId = userId;
                    if (lead.CustomFlags != null)
                    {
                        lead.CustomFlags.Add(customFlag);
                    }
                    else
                    {
                        lead.CustomFlags = new List<CustomFlag>() { customFlag };
                    }
                    result = "Tagged";
                }
                return new(lead, result);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        public async Task UpdateLeadAppointmentAndCommunicationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                //if (lead.Appointments?.Where(i => i.UserId == lead.AssignedFrom || i.UserId == lead.AssignTo).Any() ?? false)
                //{
                //    List<LeadAppointment> leadApptAssignFroom = new();
                //    //leadApptAssignFroom = lead.Appointments.Where(i => i.UserId == lead.AssignedFrom).ToList();
                // var leadApptToAdd =  GetLeadAppointmentsToAdd(lead.Appointments.ToList(), lead, cancellationToken);
                //    if (leadApptToAdd.Any())
                //    {
                //        leadApptToAdd.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                //        await _appointmentRepo.AddRangeAsync(leadApptToAdd, cancellationToken);
                //    }
                //}
                var leadcommunications = (await _communicationRepo.ListAsync(new LeadCommunicationSpec(lead.Id, lead.AssignedFrom, lead.AssignTo), cancellationToken));
                var leadCommToAdd = GetLeadCommsToAdd(leadcommunications, lead, cancellationToken);
                //if (leadCommToAdd?.Any() ?? false)
                //{
                //    List<LeadCommunication> communications = new();
                //    communications = leadCommToAdd;
                //    communications.ForEach(i => { i.Id = Guid.NewGuid(); i.UserId = lead.AssignTo; });
                //    await _communicationRepo.AddRangeAsync(communications, cancellationToken);
                //}
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private List<LeadAppointment> GetLeadAppointmentsToAdd(List<LeadAppointment> leadAppointments, Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var leadAppointmentsAssignTo = leadAppointments.Where(i => i.UserId == lead.AssignTo).DistinctBy(comm => comm.UniqueKey).ToList();
            var leadAppointmentsAssignFrom = leadAppointments.Where(i => i.UserId == lead.AssignedFrom).DistinctBy(comm => comm.UniqueKey).ToList();
            List<LeadAppointment> leadAppointmentsToAdd = new();
            leadAppointmentsAssignFrom.ForEach(appt =>
            {
                var leadAppt = leadAppointmentsAssignTo.FirstOrDefault(subAppt =>
                                                    (subAppt.Type == appt.Type
                                                 && subAppt.IsDone == appt.IsDone
                                                 && subAppt.Longitude == appt.Longitude
                                                 && subAppt.Latitude == appt.Latitude
                                                 && subAppt.LocationId == appt.LocationId
                                                 && subAppt.CreatedBy == appt.CreatedBy
                                                 && subAppt.CreatedOn == appt.CreatedOn
                                                 && subAppt.ProjectName == appt.ProjectName
                                                 && subAppt.IsFullyCompleted == appt.IsFullyCompleted
                                                 && subAppt.LastModifiedBy == appt.LastModifiedBy
                                                 && subAppt.LastModifiedOn == appt.LastModifiedOn
                                                 && subAppt.LeadId == appt.LeadId
                                                 && subAppt.UniqueKey == appt.UniqueKey));
                if (leadAppt == null)
                {
                    leadAppointmentsToAdd.Add(appt);
                }
            });
            return leadAppointmentsToAdd;
        }
        private List<LeadCommunication> GetLeadCommsToAdd(List<LeadCommunication> leadCommunications, Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var leadCommsAssignTo = leadCommunications.Where(i => i.UserId == lead.AssignTo).DistinctBy(comm => comm.UniqueKey).ToList();
            var leadCommsAssignFrom = leadCommunications.Where(i => i.UserId == lead.AssignedFrom).DistinctBy(comm => comm.UniqueKey).ToList();
            List<LeadCommunication> leadCommsToAdd = new();
            leadCommsAssignFrom.ForEach(comm =>
            {
                var leadComm = leadCommsAssignTo.FirstOrDefault(subComm =>
                                                    (subComm.ContactType == comm.ContactType
                                                 && subComm.CreatedBy == comm.CreatedBy
                                                 && subComm.CreatedOn == comm.CreatedOn
                                                 && subComm.LastModifiedBy == comm.LastModifiedBy
                                                 && subComm.LastModifiedOn == comm.LastModifiedOn
                                                 && subComm.LeadId == comm.LeadId
                                                 && subComm.UniqueKey == comm.UniqueKey));
                if (leadComm == null)
                {
                    leadCommsToAdd.Add(comm);
                }
            });
            return leadCommsToAdd;
        }

        protected async Task UpdateReassignedLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, List<UserDetailsDto> users, CancellationToken cancellationToken = default, Guid? currentUserId = null, Guid? previousAssignedFrom = null)
        {
            var existingLeadHistory = (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo))) ?? (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedFrom ?? currentUserId ?? Guid.Empty)));
            try
            {
                if (currentUserId != null && currentUserId != default && users != null && !(users.Any(i => i.Id == currentUserId)))
                {
                    var currentUser = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, cancellationToken);
                    if (currentUser != null && users.Any(i => i.Id != currentUser.Id))
                    {
                        users.Add(currentUser);
                    }
                }
            }
            catch (Exception ex) { }

            var leadDto = lead.Adapt<ViewLeadDto>();
            if (leadDto != null)
            {
                leadDto.AssignmentType = assignmentType;
                if (currentUserId == null)
                {
                    leadDto.LastModifiedBy = Guid.Empty;
                }
            }

            await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, currentUserId);
            var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            if (existingLeadHistory != null)
            {
                switch (assignmentType)
                {
                    case LeadAssignmentType.WithoutHistory:
                        await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                        break;
                    case LeadAssignmentType.WithoutHistoryWithNewStatus:
                        await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                        break;
                    case LeadAssignmentType.WithHistory:
                        await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory));
                        break;
                }
            }
            else
            {
                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                if (existingLeadHistory != null)
                {
                    switch (assignmentType)
                    {
                        case LeadAssignmentType.WithoutHistory:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithoutHistoryWithNewStatus:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithHistory:
                            await KeepOldHistoryForLeadReassignmentAsync(existingLeadHistory, leadDto, users, cancellationToken);
                            break;
                    }
                }
                else if (existingLeadHistory == null)
                {
                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken);
                }
            }
        }


        protected async Task AddNewHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, LeadHistory newHistory, List<UserDetailsDto> users, ViewLeadDto fullLeadDto, bool withNewStatus, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUser.GetUserId()));
                if (currentUser == null)
                {
                    try
                    {
                        currentUser = (await _userService.GetListOfUsersByIdsAsync(new() { (currentUserId ?? _currentUser.GetUserId()).ToString() }, cancellationToken)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignedFrom ?? Guid.Empty));
                var assignToUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignTo ?? Guid.Empty));
                existingLeadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(existingLeadHistory, cancellationToken);
                }
                var leadHistory = existingLeadHistory.MapV1LeadHistory(currentUser, assignedFromUser, assignToUser, withNewStatus);
                leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newHistory, leadHistory);
                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, AppointmentType appointmentType, CancellationToken cancellationToken)
        {
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        public async Task<LeadHistory?> UpdateHistoryForLeadReassignmentAsync(LeadHistory leadHistory, UserDetailsDto? assignedFromUser, UserDetailsDto? assignToUser, UserDetailsDto? currentUser)
        {
            try
            {
                if (leadHistory != null)
                {               
                    var version = leadHistory.CurrentVersion + 1;
                    var name = !string.IsNullOrWhiteSpace($"{currentUser?.FirstName} {currentUser?.LastName}".Trim())? $"{currentUser?.FirstName} {currentUser?.LastName}".Trim(): "System";

                    if (leadHistory.LastModifiedBy == null)
                    {
                        leadHistory.LastModifiedBy = new Dictionary<int, string>();
                    }
                    if (!leadHistory.LastModifiedBy.ContainsKey(version))
                    {
                        leadHistory.LastModifiedBy.Add(version, name);
                    }
                    if (leadHistory?.AssignedTo?.LastOrDefault().Value != assignToUser?.Id)
                    {
                        leadHistory.AssignedTo?.Add(version, assignToUser?.Id ?? default);
                    }
                    if (!(string.IsNullOrEmpty(leadHistory?.AssignedToUser?.LastOrDefault().Value)) && leadHistory?.AssignedToUser?.LastOrDefault().Value != $"{assignToUser?.FirstName} {assignToUser?.LastName}")
                    {
                        leadHistory.AssignedToUser?.Add(version, $"{assignToUser?.FirstName} {assignToUser?.LastName}" ?? string.Empty);
                    }
                    if (!(string.IsNullOrEmpty(leadHistory?.AssignedFromUser?.LastOrDefault().Value)) && leadHistory?.AssignedFromUser?.LastOrDefault().Value != $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}")
                    {
                        leadHistory.AssignedFromUser?.Add(version, $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}" ?? string.Empty);

                    }
                    leadHistory.ModifiedDate?.Add(version, DateTime.UtcNow);
                    leadHistory.CurrentVersion = version;
                }
                return leadHistory;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUserRepo.GetUserId())
                {
                    var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                    var tenantId = _currentUserRepo.GetTenant();
                    var currentUserId = _currentUserRepo.GetUserId();
                    await SendNotificationToAdminsAndReporteesAsync(lead, leadDto, globalSettings, tenantId, currentUserId);
                }
                else if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != _currentUserRepo.GetUserId()).ToList();
                    if (adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            try
                            {
                                var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                                if (adminDetails != null)
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: 1, globalSettings: globalSettings);
                                }
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                        }
                    }
                }
                else if (globalSettings?.IsEngageToEnabled ?? false && lead.AssignTo != default && lead.AssignTo != Guid.Empty)
                {
                    var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.Engegato, lead, lead.AssignTo, leadDto.AssignedUser?.Name, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, globalSettings: globalSettings);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SendLeadAssignmentNotificationsV2Async(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead.AssignTo != default && lead.AssignTo != Guid.Empty && lead.AssignTo != _currentUserRepo.GetUserId())
                {
                    var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                    var data = new SendNotificationDto() { @event = Event.LeadAssignment, Entity = lead, AssignTo = lead.AssignTo, UserName = leadDto.AssignedFromUser?.Name, Topics = new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, CurrentUserId = _currentUserRepo.GetUserId(), TenantId = _currentUser.GetTenant() ?? string.Empty };
                    var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "notification", data);
                    await _serviceBus.RunNotificationJobAsync(payload);
                }
                else if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != _currentUserRepo.GetUserId()).ToList();
                    if (adminIds.Any())
                    {
                        foreach (var adminId in adminIds)
                        {
                            var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            try
                            {
                                if (adminDetails != null)
                                {
                                    var data = new SendNotificationDto() { @event = Event.LeadMovedToUnassigned, Entity = lead, AssignTo = adminId, UserName = null, CurrentUserId = _currentUserRepo.GetUserId(), NoOfEntities = 1, TenantId = _currentUser.GetTenant() ?? string.Empty };
                                    var payload = new InputPayload(_currentUser.GetTenant() ?? string.Empty, _currentUserRepo.GetUserId(), "notification", data);
                                    await _serviceBus.RunNotificationJobAsync(payload);
                                }
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, int leadCount, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != _currentUserRepo.GetUserId()).ToList();
                    if (adminIds.Any())
                    {
                        //foreach (var adminId in adminIds)
                        //{
                        //    //var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                        //    var adminDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { adminId.ToString() }, cancellationToken)).FirstOrDefault();
                        //    if (adminDetails != null)
                        //    {
                        //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: leadCount);
                        //    }
                        //}
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, null, noOfEntities: leadCount, userIds: adminIds, globalSettings: globalSettings);
                    }
                }
                else
                {
                    //var assignedUser = await _userService.GetAsync(lead.AssignTo.ToString() ?? string.Empty, cancellationToken);
                    var assignedUser = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { lead?.AssignTo.ToString() ?? string.Empty }, cancellationToken)).FirstOrDefault();
                    if (assignedUser != null && _currentUser.GetUserId() != assignedUser.Id)
                    {
                        if (leadCount > 1)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leadCount, globalSettings: globalSettings);
                        }
                        else
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName);
                        }
                    }
                    else if (globalSettings?.IsEngageToEnabled ?? false)
                    {
                        if (leadCount > 1)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leadCount, globalSettings: globalSettings);
                        }
                        else
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SendLeadUpdateNotificationsAsync(Domain.Entities.Lead lead, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken);
                var currentUserId = _currentUserRepo.GetUserId();
                await lead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUserRepo.GetUserId());
                if (lead != null && lead.AssignTo != currentUserId)
                {
                    var userDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                    if (userDetails != null)
                    {
                        await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadInfoUpdate, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName, globalSettings: globalSettings);
                        var tenantId = _currentUserRepo.GetTenant();
                        await SendNotificationToAdminsAndReporteesAsync(lead, leadDto, globalSettings, tenantId, currentUserId);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SendOnlyLeadInfoUpdateNotificationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        {
            try
            {
                var currentUserId = _currentUserRepo.GetUserId();
                if (lead != null && lead.AssignTo != Guid.Empty && lead.AssignTo != currentUserId)
                {
                    var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                    if (user != null)
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadInfoUpdate, lead, user.Id, user.FirstName + " " + user.LastName);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SendLeadRestoreNotificationAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, Dictionary<Guid, int>? groupedUsersWithLeadCount = null)
        {
            try
            {
                if (groupedUsersWithLeadCount == null)
                {
                    return;
                }
                foreach (var entry in groupedUsersWithLeadCount)
                {
                    var currentUserId = _currentUser.GetUserId();
                    if (entry.Key != Guid.Empty && entry.Key != currentUserId)
                    {
                        var user = (await _userService.GetListAsync(cancellationToken)).Where(i => i.Id == lead.AssignTo).FirstOrDefault();
                        if (user != null)
                        {
                            await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.RestoreLead, lead, user.Id, user.FirstName + " " + user.LastName, noOfEntities: entry.Value, userIds: new() { entry.Key }, currentUserIdPm: currentUserId);
                        }
                        //await _notificationSenderService.ScheduleNotificationsAsync(Event.RestoreLead, lead, noOfEntities: entry.Value, userIds: new() { entry.Key });
                    }
                    else if (entry.Key == Guid.Empty)
                    {
                        List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(_currentUser.GetTenant() ?? string.Empty)).Where(i => i != currentUserId).ToList();
                        if (adminIds.Any())
                        {
                            //foreach (var adminId in adminIds)
                            //{
                            //    var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                            //    if (adminDetails != null)
                            //    {
                            //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.RestoreLead, lead, adminId, adminDetails.FirstName + " " + adminDetails.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, noOfEntities: entry.Value, userIds: new() { entry.Key }, currentUserIdPm: currentUserId);
                            //    }
                            //}
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.RestoreLead, lead, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, noOfEntities: entry.Value, userIds: adminIds, currentUserIdPm: currentUserId);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task SendLeadAppointmentNotificationAsync(Domain.Entities.Lead lead, AppointmentType appointmentType, bool isDone, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken = default)
        {
            try
            {
                //var globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = await GetFullLeadDtoAsync(fullLead, cancellationToken);

                try
                {
                    await fullLead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                }
                catch
                {

                }
                Event? @event = null;
                if (appointmentType != AppointmentType.None)
                {
                    switch (appointmentType)
                    {
                        case AppointmentType.SiteVisit:
                            switch (isDone)
                            {
                                case true:
                                    @event = Event.LeadSiteVisitDone;
                                    break;
                                case false:
                                    @event = Event.LeadSiteVisitNotDone;
                                    break;
                            }
                            break;
                        case AppointmentType.Meeting:
                            switch (isDone)
                            {
                                case true:
                                    @event = Event.LeadMeetingDone;
                                    break;
                                case false:
                                    @event = Event.LeadMeetingNotDone;
                                    break;
                            }
                            break;
                    }
                    if (@event != null)
                    {
                        Event updatedEvent = (Event)@event;
                        try
                        {
                            if (fullLead.AssignTo != Guid.Empty)
                            {
                                var userDetails = await _userService.GetAsync(fullLead.AssignTo.ToString(), cancellationToken);
                                if (userDetails.Id != _currentUser.GetUserId())
                                {
                                    await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, fullLead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName);
                                }
                            }
                        }
                        catch
                        {

                        }

                        try
                        {
                            await _notificationSenderService.SendWhatsAppNotificationAsync(fullLead, updatedEvent, null, globalSettings ?? new(), true);
                        }
                        catch (Exception ex)
                        {
                            _logger.Information($"SendLeadAppointmentNotificationAsync -> Error While Sending WhatsApp Notification! {ex}");
                        }


                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task UpdateLeadTagInfoAsync(Domain.Entities.Lead lead, LeadTagDto? leadTagDto, CancellationToken cancellationToken = default)
        {
            try
            {
                var leadTags = leadTagDto?.Adapt<LeadTag>() ?? new();
                var existingTags = lead.TagInfo;
                if (existingTags != null)
                {
                    existingTags.Update(leadTags);
                    await _leadTagRepo.UpdateAsync(existingTags, cancellationToken);
                }
                else
                {
                    leadTags.LeadId = lead.Id;
                    await _leadTagRepo.AddAsync(leadTags, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        protected async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = moduleName
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            catch
            {
                throw;
            }
        }

        private async Task<List<LeadDocument>> CreateTrueLeadDocsAsync(List<LeadDocument> documentsToAdd, Domain.Enums.LeadDocumentType documentType)
        {
            try
            {
                var currentUserId = _currentUserRepo.GetUserId();
                List<LeadDocument> documents = new();
                foreach (var document in documentsToAdd)
                {
                    document.Id = Guid.NewGuid();
                    document.DocumentName = document.DocumentName;
                    document.UploadedOn = DateTime.UtcNow;
                    document.CreatedBy = currentUserId;
                    document.LastModifiedBy = currentUserId;
                    document.CreatedOn = DateTime.UtcNow;
                    document.LastModifiedOn = DateTime.UtcNow;
                    document.LeadDocumentType = documentType;
                    documents.Add(document);
                }
                return documents;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        private async Task SetTruePropertiesAsync(Domain.Entities.Lead lead, List<PropertyDto>? properties, CancellationToken cancellationToken = default)
        {
            try
            {
                var trueProperties = properties?.Where(i => !string.IsNullOrWhiteSpace(i.Title)).ToList();
                if (trueProperties != null && trueProperties.Any())
                {
                    var propertyTitles = trueProperties.Select(i => i.Title ?? "Invalid")?.ToList();
                    var newProperties = await _propertyRepo.ListAsync(new GetAllPropertiesForIntegrationSpec(propertyTitles), cancellationToken);
                    lead.Properties = newProperties;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }

        private async Task SetTrueProjectsAsync(Domain.Entities.Lead lead, List<ProjectDto>? projects, CancellationToken cancellationToken = default)
        {
            try
            {
                var trueProjects = projects?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).ToList();
                if (trueProjects != null && trueProjects.Any())
                {
                    var projectNames = trueProjects.Select(i => i.Name ?? "Invalid")?.ToList();
                    var newProjects = await _projectRepo.ListAsync(new V2GetAllProjectsForIntegrationSpec(projectNames), cancellationToken);
                    lead.Projects = newProjects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        private async Task SetDuplicateLeadEnquiryAsync(Domain.Entities.Lead lead, ViewLeadEnquiryDto? enquiryDto, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Enquiries ??= new List<LeadEnquiry>();
                var duplicateEnquiry = new LeadEnquiry();
                if (enquiryDto != null)
                {
                    duplicateEnquiry = enquiryDto.Adapt<LeadEnquiry>();
                    if (enquiryDto.PropertyTypes != null && enquiryDto.PropertyTypes.Select(i => i.ChildType).Count() > 0)
                    {
                        duplicateEnquiry.PropertyTypes = await ValidatePropertyTypesAsync(enquiryDto.PropertyTypes.Select(i => i.ChildType.Id).ToList(), cancellationToken);
                        duplicateEnquiry.PropertyType = duplicateEnquiry?.PropertyTypes?.FirstOrDefault();

                    }
                    //if (duplicateEnquiry.Address != null)
                    //{
                    //    var locationId = duplicateEnquiry.Address.LocationId;
                    //    duplicateEnquiry.Address.Id = Guid.NewGuid();
                    //    duplicateEnquiry.Address.Location = null;
                    //    duplicateEnquiry.Address.LocationId = locationId;
                    //}
                    if (duplicateEnquiry.Addresses != null)
                    {
                        foreach (var address in duplicateEnquiry.Addresses)
                        {
                            if (address != null)
                            {
                                var locationId = address.LocationId;
                                address.Id = Guid.NewGuid();
                                address.Location = null;
                                address.LocationId = locationId;
                            }
                        }
                    }
                }
                if (assignmentDto.UpdateSource)
                {
                    duplicateEnquiry.LeadSource = assignmentDto.LeadSource;
                }
                if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                {
                    duplicateEnquiry.SubSource = assignmentDto.SubSource;
                }
                duplicateEnquiry.Id = Guid.NewGuid();
                lead.Enquiries.Add(duplicateEnquiry);

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            try
            {
                var location = address.MapToLocationRequest();
                if (location != null)
                {
                    var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                    var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                    address.Location = createdLocation;
                    if (address.Location != null)
                    {
                        await _addressRepo.UpdateAsync(address);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }

        private static string SetLeadTags(LeadTag leadTag, LeadTagEnum? leadTags)
        {
            string message = string.Empty;
            switch (leadTags)
            {
                case LeadTagEnum.IsHot:
                    leadTag.IsHotLead = !leadTag.IsHotLead;
                    message = leadTag.IsHotLead ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsAboutToConvert:
                    leadTag.IsAboutToConvert = !leadTag.IsAboutToConvert;
                    message = leadTag.IsAboutToConvert ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsEscalated:
                    leadTag.IsEscalated = !leadTag.IsEscalated;
                    message = leadTag.IsEscalated ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsIntegrationLead:
                    leadTag.IsIntegrationLead = !leadTag.IsIntegrationLead;
                    message = leadTag.IsIntegrationLead ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsHighlighted:
                    leadTag.IsHighlighted = !leadTag.IsHighlighted;
                    message = leadTag.IsHighlighted ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsWarmLead:
                    leadTag.IsWarmLead = !leadTag.IsWarmLead;
                    message = leadTag.IsWarmLead ? "Flagged" : "DeFlagged";
                    break;
                case LeadTagEnum.IsColdLead:
                    leadTag.IsColdLead = !leadTag.IsColdLead;
                    message = leadTag.IsColdLead ? "Flagged" : "DeFlagged";
                    break;
                default:
                    break;
            }
            return message;
        }
        protected async Task<bool> ShouldUpdatePickedDate(Domain.Entities.Lead existingLead, PickedLeadDto pickedLeadDto, bool? isLeadFormUpdated = null, Guid? currentUserId = null)
        {
            if (pickedLeadDto.AssignTo != null && pickedLeadDto.AssignTo != Guid.Empty && existingLead.AssignTo != pickedLeadDto.AssignTo)
            {
                existingLead.PickedDate = null;
                existingLead.IsPicked = false;
                return false;
            }
            bool? shouldUpdatePickedDate = null;
            currentUserId ??= _currentUserRepo.GetUserId();
            var originalLead = existingLead.CreateDeepCopy();
            if (originalLead != null &&
                pickedLeadDto != null &&
                (currentUserId != default && currentUserId != Guid.Empty) &&
                originalLead.AssignTo != default && (originalLead.AssignTo == currentUserId || originalLead.SecondaryUserId == currentUserId) &&
                originalLead.PickedDate == null)
            {
                shouldUpdatePickedDate = originalLead.GetPickedDateAsync(pickedLeadDto, isLeadFormUpdated: isLeadFormUpdated).Result;
            }
            return shouldUpdatePickedDate ?? false;
        }

        protected async Task SetQRSubSourceAsync(Domain.Entities.Lead lead, Guid? templateId, CancellationToken cancellationToken)
        {
            var enquiry = lead.Enquiries.FirstOrDefault();
            if (templateId != null && templateId != Guid.Empty && enquiry?.LeadSource == LeadSource.QRCode)
            {
                var template = await _qrFormTemplateRepo.GetByIdAsync(templateId ?? Guid.Empty, cancellationToken);
                if (template != null)
                {
                    enquiry.SubSource = template.Name?.ToLower();
                }
            }
        }
        protected async Task SetQRAgencyName(Domain.Entities.Lead lead, Guid? templateId, CancellationToken cancellationToken)
        {
            if (templateId != null || templateId != Guid.Empty)
            {
                //var template = await _qrFormTemplateRepo.GetByIdAsync(templateId ?? Guid.Empty, cancellationToken);
                var template = await _qrFormTemplateRepo.FirstOrDefaultAsync(new GetQRFormTemplateByIdSpecs(templateId ?? Guid.Empty), cancellationToken);
                if (template != null)
                {
                    lead.AgencyName = template.AgencyName;
                    lead.Agencies = template.Agency != null ? new List<Domain.Entities.Agency>() { template.Agency } : lead.Agencies;
                }
            }

        }

        protected async Task SendLeadForAutoAssignment(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var globalSetting = (await _globalsettingRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            if (globalSetting != null && globalSetting.IsLeadRotationEnabled)
            {
                if (lead.AssignTo != Guid.Empty)
                {
                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id);
                }
            }
        }
        // Dreamyard and kroft are the only tenants that currently use this feature
        protected async Task AutoReassignmentHandler(Domain.Entities.Lead lead, CancellationToken cancellationToken)
        {
            var tenant = _currentUser.GetTenant();
            List<Guid> leadIds = new() { lead.Id };

            if (tenant == "dreamyard" || tenant == "kroft" || tenant == "custom") 
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusId(lead?.CustomLeadStatus?.Id ?? Guid.Empty), cancellationToken);
                if (team != null)
                {
                    var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(lead?.Id ?? Guid.Empty), cancellationToken);
                    if (tracker == null)
                    {
                        #region Add Lead Rotation Tracker
                        var newTracker = new LeadRotationTracker();
                        newTracker.LeadId = lead?.Id;
                        newTracker.NoOfRotation = team.NoOfReassignment;
                        await _rotationTrackerRepo.AddAsync(newTracker);
                        #endregion
                    }

                    AutoReassignmentForDreamyardandKroft entity = new()
                    {
                        LeadIds = leadIds
                    };
                    InputPayloadV2 payload = new(tenant, entity);
                    //var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                    await _serviceBus.RunLeadRotationHttpTriggerJobAsync(payload);
                }
            }
        }

        public async Task ScheduleLeadRetentionRotation(List<Domain.Entities.Lead> leads, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {
            //var globalSetting = (await _globalsettingRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsTeamLeadRotationEnabled)
            {
                await _leadRotationService.ScheduleTeamRetentionRotation(leads);
            }
        }

        public async Task<Guid> GetAutoRetentionAssignmentId(Domain.Entities.Lead lead, Guid statusId)
        {
            Guid newAssignmentId = Guid.Empty;
            var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusIdSpecs(statusId));
            var currentWeek = lead?.LastModifiedOn?.DayOfWeek;
            if (team != null && (team?.Configuration?.DayOfWeeks?.Contains(currentWeek.Value) ?? false) && (team.UserIds?.Any() ?? false))
            {
                List<string>? groupUserIds = (team.UserIds.Select(i => i.ToString())).ToList();

                var userIds = await _userService.GetListOfUsersByIdsAsync(groupUserIds, default);

                var activeUser = userIds.Where(i => i.IsActive).Select(i => i.Id).ToList();

                var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerByCreatedOnSpecs());

                if (tracker != null && (tracker.AssignedUsers?.Any() ?? false))
                {
                    var firstAssignedUser = tracker.AssignedUsers?.OrderBy(i => i.Key).Select(i => i.Value).FirstOrDefault();

                    var notAssignedUser = activeUser.IndexOf(firstAssignedUser ?? Guid.Empty);
                    if (notAssignedUser < (activeUser.Count - 1))
                    {
                        var assignTo = activeUser[notAssignedUser + 1];
                        newAssignmentId = assignTo;
                    }
                    else
                    {
                        var assignTo = activeUser[0];
                        newAssignmentId = assignTo;
                    }
                }
                else
                {
                    var assignTo = activeUser.FirstOrDefault();
                    newAssignmentId = assignTo;
                }
            }
            return newAssignmentId;
        }
        protected async Task<bool> CreateLeadAssignmentHistory(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, LeadAssignmentDto? assignmentDto = null, CancellationToken cancellationToken = default, bool isDuplicate = default)
        {
            var leadAssignmentHistories = await _leadAssignmentRepo.ListAsync(new GetLeadAssignmentsByIdSpecs(lead.Id));
            if (leadAssignmentHistories == null)
            {
                var assignment = new LeadAssignment()
                {
                    AssignTo = lead.AssignTo,
                    AssignedFrom = lead.AssignedFrom,
                    Notes = lead.Notes,
                    LeadId = lead.Id,
                    UserId = lead.AssignTo,
                    LeadAssignmentType = assignmentType,
                    AssignmentDate = DateTime.UtcNow,
                };
                if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignTo = lead.SecondaryUserId;
                }
                if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                }
                if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                {
                    assignment.ProjectName = assignmentDto?.Projects?.ToString();
                    assignment.SourceName = assignmentDto?.LeadSource.ToString();
                }
                if (isDuplicate)
                {
                    assignment.IsDuplicate = true;
                }
                await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
            }
            else
            {
                var leadLastAssignment = leadAssignmentHistories?.LastOrDefault();
                if (leadLastAssignment?.AssignTo != lead?.AssignTo)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
                else if (lead?.SecondaryUserId != null && leadLastAssignment?.SecondaryAssignTo != lead.SecondaryUserId)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
            }
            return true;
        }
        private async Task SendNotificationToAdminsAndReporteesAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto, Domain.Entities.GlobalSettings globalSettings, string tenantId, Guid currentUserId)
        {
            leadDto ??= await GetFullLeadDtoAsync(lead, CancellationToken.None, currentUserId);
            var userInfo = (await _dapperRepository.GetUserDetailsAsync(tenantId ?? string.Empty, new List<Guid> { lead.AssignTo }))?.FirstOrDefault();
            List<Guid> adminIds = (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty)).Where(i => i != (currentUserId)).ToList();
            var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty) ?? new();
            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
            {
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: adminIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo != null) && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo?.ReportsTo.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo != null) && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.GeneralManager.Id == i)) && (userInfo.GeneralManager.Id != currentUserId))
            {
                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, leadDto.AssignedUser?.Name, globalSettings: globalSettings, userIds: userIds);
            }
        }
        protected async Task UpdateChannelPartenerForMultipleLeadsAsync(List<Domain.Entities.Lead> leads, List<Lrb.Domain.Entities.ChannelPartner> channelPartners, bool ShouldRemoveExistingChannelPartener, CancellationToken cancellationToken = default, Guid? userId = null,string? tenantId=null)
        {
            try
            {
                var allLeads = new List<Domain.Entities.Lead>();
                var currentUserId = userId ?? _currentUser.GetUserId();
                foreach (var lead in leads)
                {
                    try
                    {
                        lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, new() { ChannelPartners = channelPartners.Adapt<List<ChannelPartnerDto>>() });
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                    if ((lead.ChannelPartners?.Any() ?? false) && !ShouldRemoveExistingChannelPartener)
                    {
                        List<Lrb.Domain.Entities.ChannelPartner> newChannelPartners = new();
                        newChannelPartners.AddRange(lead.ChannelPartners);
                        newChannelPartners.AddRange(channelPartners);
                        newChannelPartners = newChannelPartners.DistinctBy(p => p.Id).ToList();
                        lead.ChannelPartners = newChannelPartners;
                    }
                    else
                    {
                        lead.ChannelPartners = channelPartners;
                    }
                    lead.LastModifiedOn = DateTime.UtcNow;
                    lead.LastModifiedBy = currentUserId;
                    allLeads.Add(lead);

                }
                if (allLeads?.Any() ?? false)
                {
                    var leadChannelpartners = leads.Where(lead => lead.ChannelPartners != null && lead.ChannelPartners.Any()).SelectMany(lead => lead.ChannelPartners.Select(i => new ChannelPartnerLeadDto
                    {
                        LeadsId = lead.Id,
                        ChannelPartnersId = i.Id
                    })).ToList();
                    if (leadChannelpartners.Any())
                    {
                        if (ShouldRemoveExistingChannelPartener)
                        {
                            var leadIdsToClear = allLeads.Select(l => l.Id).Distinct().ToList();
                            if (leadIdsToClear.Any())
                            {
                                var leadIdList = string.Join(", ", leadIdsToClear.Select(id => $"'{id}'"));
                                var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"ChannelPartnerLead\" WHERE \"LeadsId\" IN ({leadIdList});";
                                await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                            }
                        }
                        List<string> columnNames = new List<string> { "ChannelPartnersId", "LeadsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "ChannelPartnerLead", columnNames, leadChannelpartners);
                        await _dapperRepository.ExecuteQueryAsync(campaignInsertQuery);
                    }
                    allLeads = await _leadRepo.ListAsync(new LeadByIdSpec(allLeads?.Select(i => i.Id).ToList()), cancellationToken);
                    List<ViewLeadDto> leadDtos = new List<ViewLeadDto>();
                    List<Domain.Entities.LeadHistory>? existingLeadHistories = new();
                    List<Domain.Entities.LeadHistory>? newLeadHistories = new();


                    foreach (var oldlead in allLeads)
                    {
                        var leadDto = await GetFullLeadDtoAsync(oldlead, cancellationToken, currentUserId: currentUserId);
                        leadDtos.Add(leadDto);
                        var leadHistories = await UpdateLeadHistoryAsyncV2(oldlead, leadDto: leadDto, null, cancellationToken: cancellationToken, null, currentUserId: null);
                        if (leadHistories.Item1 != null)
                        {
                            existingLeadHistories.Add(leadHistories.Item1);
                        }
                        if (leadHistories.Item2 != null)
                        {
                            newLeadHistories.Add(leadHistories.Item2);
                        }
                    }
                    if (existingLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = existingLeadHistories.FirstOrDefault()?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(i => i.Name)
                        .ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateUpdateLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, existingLeadHistories);
                        await _dapperRepository.ExecuteQueryAsync(historyquery);

                    }
                    if (newLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = newLeadHistories?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any()).Select(i => i.Name).ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateInsertLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, newLeadHistories);
                    }
                }

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateAgencyForMultipleLeadsAsync(List<Domain.Entities.Lead> leads, List<Lrb.Domain.Entities.Agency> agency, bool ShouldRemoveExistingAgency, CancellationToken cancellationToken = default, Guid? userId = null, string? tenantId = null)
        {
            try
            {
                var allLeads = new List<Domain.Entities.Lead>();
                var currentUserId = userId ?? _currentUser.GetUserId();
                foreach (var lead in leads)
                {
                    try
                    {
                        lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, new() { Agency = agency.Adapt<List<AgencyDto>>() });

                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                    if ((lead.Agencies?.Any() ?? false) && !ShouldRemoveExistingAgency)
                    {
                        List<Lrb.Domain.Entities.Agency> newAgency = new();
                        newAgency.AddRange(lead.Agencies);
                        newAgency.AddRange(agency);
                        newAgency = newAgency.DistinctBy(p => p.Id).ToList();
                        lead.Agencies = newAgency;
                    }
                    else
                    {
                        lead.Agencies = agency;
                    }
                    lead.LastModifiedOn = DateTime.UtcNow;
                    lead.LastModifiedBy = currentUserId;
                    allLeads.Add(lead);

                }
                if (allLeads?.Any() ?? false)
                {
                    var leadAgencies = leads.Where(lead => lead.Agencies != null && lead.Agencies.Any()).SelectMany(lead => lead.Agencies.Select(i => new AgencyLeadDto
                   {
                       LeadsId = lead.Id,
                       AgenciesId = i.Id
                   })).ToList();
                    if (leadAgencies.Any())
                    {
                        if (ShouldRemoveExistingAgency)
                        {
                            var leadIdsToClear = allLeads.Select(l => l.Id).Distinct().ToList();
                            if (leadIdsToClear.Any())
                            {
                                var leadIdList = string.Join(", ", leadIdsToClear.Select(id => $"'{id}'"));
                                var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"AgencyLead\" WHERE \"LeadsId\" IN ({leadIdList});";
                                await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                            }
                        }
                        List<string> columnNames = new List<string> { "AgenciesId", "LeadsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "AgencyLead", columnNames, leadAgencies);
                        await _dapperRepository.ExecuteQueryAsync(campaignInsertQuery);
                    }

                    allLeads = await _leadRepo.ListAsync(new LeadByIdSpec(allLeads?.Select(i => i.Id).ToList()), cancellationToken);
                    List<ViewLeadDto> leadDtos = new List<ViewLeadDto>();
                    List<Domain.Entities.LeadHistory>? existingLeadHistories = new();
                    List<Domain.Entities.LeadHistory>? newLeadHistories = new();


                    foreach (var oldlead in allLeads)
                    {
                        var leadDto = await GetFullLeadDtoAsync(oldlead, cancellationToken, currentUserId: currentUserId);
                        leadDtos.Add(leadDto);
                        var leadHistories = await UpdateLeadHistoryAsyncV2(oldlead, leadDto: leadDto, null, cancellationToken: cancellationToken, null, currentUserId: null);
                        if (leadHistories.Item1 != null)
                        {
                            existingLeadHistories.Add(leadHistories.Item1);
                        }
                        if (leadHistories.Item2 != null)
                        {
                            newLeadHistories.Add(leadHistories.Item2);
                        }
                    }
                    if (existingLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = existingLeadHistories.FirstOrDefault()?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(i => i.Name)
                        .ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateUpdateLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, existingLeadHistories);
                        await _dapperRepository.ExecuteQueryAsync(historyquery);

                    }
                    if (newLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = newLeadHistories?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any()).Select(i => i.Name).ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateInsertLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, newLeadHistories);
                    }
                }

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateCampaignForMultipleLeadsAsync(List<Domain.Entities.Lead> leads, List<Lrb.Domain.Entities.Campaign> campaign, bool ShouldRemoveExistingCampaign, CancellationToken cancellationToken = default, Guid? userId = null, string? tenantId = null)
        {
            try
            {
                var allLeads = new List<Domain.Entities.Lead>();
                var currentUserId = userId ?? _currentUser.GetUserId();
                foreach (var lead in leads)
                {
                    try
                    {
                        lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(lead, new() { Campaigns = campaign.Adapt<List<CampaignsDtoV1>>() });

                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                    if ((lead.Campaigns?.Any() ?? false) && !ShouldRemoveExistingCampaign)
                    {
                        List<Lrb.Domain.Entities.Campaign> newCampaigns = new();
                        newCampaigns.AddRange(lead.Campaigns);
                        newCampaigns.AddRange(campaign);
                        newCampaigns = newCampaigns.DistinctBy(p => p.Id).ToList();
                        lead.Campaigns = newCampaigns;
                    }
                    else
                    {
                        lead.Campaigns = campaign;
                    }
                    lead.LastModifiedOn = DateTime.UtcNow;
                    lead.LastModifiedBy = currentUserId;
                    allLeads.Add(lead);

                }
                if (allLeads?.Any() ?? false)
                {
                    var leadCampaigns = allLeads
                  .Where(i => i.Campaigns != null && i.Campaigns.Any())
                  .SelectMany(j => j.Campaigns.Select(i => new Lrb.Application.Lead.Web.Dtos.CampaignLeadDto
                  {
                      LeadsId = j.Id,
                      CampaignsId = i.Id
                  })).ToList();
                    if (leadCampaigns.Any())
                    {
                        if (ShouldRemoveExistingCampaign)
                        {
                            var leadIdsToClear = allLeads.Select(l => l.Id).Distinct().ToList();
                            if (leadIdsToClear.Any())
                            {
                                var leadIdList = string.Join(", ", leadIdsToClear.Select(id => $"'{id}'"));
                                var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"CampaignLead\" WHERE \"LeadsId\" IN ({leadIdList});";
                                await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                            }
                        }
                        List<string> columnNames = new List<string> { "CampaignsId", "LeadsId" };
                        var campaignInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "CampaignLead", columnNames, leadCampaigns);
                        await _dapperRepository.ExecuteQueryAsync(campaignInsertQuery);
                    }
                    allLeads = await _leadRepo.ListAsync(new LeadByIdSpec(allLeads?.Select(i => i.Id).ToList()), cancellationToken);
                    List<ViewLeadDto> leadDtos = new List<ViewLeadDto>();
                    List<Domain.Entities.LeadHistory>? existingLeadHistories = new();
                    List<Domain.Entities.LeadHistory>? newLeadHistories = new();
                    foreach (var oldlead in allLeads)
                    {
                        var leadDto = await GetFullLeadDtoAsync(oldlead, cancellationToken, currentUserId: currentUserId);
                        leadDtos.Add(leadDto);
                        var leadHistories = await UpdateLeadHistoryAsyncV2(oldlead, leadDto: leadDto, null, cancellationToken: cancellationToken, null, currentUserId: null);
                        if (leadHistories.Item1 != null)
                        {
                            existingLeadHistories.Add(leadHistories.Item1);
                        }
                        if (leadHistories.Item2 != null)
                        {
                            newLeadHistories.Add(leadHistories.Item2);
                        }
                    }
                    if (existingLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = existingLeadHistories.FirstOrDefault()?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(i => i.Name)
                        .ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateUpdateLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, existingLeadHistories);
                        await _dapperRepository.ExecuteQueryAsync(historyquery);

                    }
                    if (newLeadHistories?.Any() ?? false)
                    {
                        var historyproperties = newLeadHistories?.GetType().GetProperties().Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any()).Select(i => i.Name).ToList() ?? new List<string>();
                        var historyquery = QueryGenerator.GenerateInsertLeadHistoryQuery(tenantId, DataBaseDetails.LRBSchema, "LeadHistories", historyproperties, newLeadHistories);
                    }
                }

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
    }
    public record InputPayload(string TenantId, Guid CurrentUserId, string Type, object Entity);

    public record InputPayloadV2(string TenantId, object Entity);

    public class AutoReassignmentForDreamyardandKroft
    {
        public List<Guid>? LeadIds { get; set; }
    }

}
