﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Project.Web.Mappings
{
    public static class UnitHelper
    {
        public static (List<UnitType>, List<CustomFieldValue>) ConvertToUnit(this DataTable dataTable,
            Dictionary<UnitDataColumn, string> dataColumn1,
            List<MasterAreaUnit> unitTypes,
            List<MasterProjectType> projectTypes,
            List<CustomMasterAttribute> unitTypesAttributes,
            Lrb.Domain.Entities.GlobalSettings globalSettings,
            Lrb.Domain.Entities.Project project, List<CustomFormFields> formFields = null, 
            Dictionary<string, string>? CustommapColumnsData = null)
        {
            List<UnitType> units = new();
            List<CustomFieldValue> formFieldValues = new();
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

            Guid? defaultUnitId = Guid.TryParse(globalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;
            foreach (DataRow dataRow in dataTable.Rows)
            {
                string? name = !CustommapColumnsData.ContainsKey("Name") || string.IsNullOrEmpty(CustommapColumnsData["Name"]) ? null : dataRow[CustommapColumnsData["Name"]].ToString().Trim();
                string? furnishingStatus = !CustommapColumnsData.ContainsKey("FurnishingStatus") || string.IsNullOrEmpty(CustommapColumnsData["FurnishingStatus"]) ? string.Empty : dataRow[CustommapColumnsData["FurnishingStatus"]].ToString().Trim();
                string? bhkType = !CustommapColumnsData.ContainsKey("BHKType") || string.IsNullOrEmpty(CustommapColumnsData["BHKType"]) ? string.Empty : dataRow[CustommapColumnsData["BHKType"]].ToString();
                string? noOfBhks = !CustommapColumnsData.ContainsKey("NoOfBHK") || string.IsNullOrEmpty(CustommapColumnsData["NoOfBHK"]) ? string.Empty : dataRow[CustommapColumnsData["NoOfBHK"]].ToString();
                string? facing = !CustommapColumnsData.ContainsKey("Facing") || string.IsNullOrEmpty(CustommapColumnsData["Facing"]) ? string.Empty : dataRow[CustommapColumnsData["Facing"]].ToString();
                string? unitArea = !CustommapColumnsData.ContainsKey("UnitArea") || string.IsNullOrEmpty(CustommapColumnsData["UnitArea"]) ? string.Empty : dataRow[CustommapColumnsData["UnitArea"]].ToString();
                string? carpetArea = !CustommapColumnsData.ContainsKey("CarpetArea") || string.IsNullOrEmpty(CustommapColumnsData["CarpetArea"]) ? string.Empty : dataRow[CustommapColumnsData["CarpetArea"]].ToString();
                string? carpetAreaUnit = !CustommapColumnsData.ContainsKey("CarpetAreaUnit") || string.IsNullOrEmpty(CustommapColumnsData["CarpetAreaUnit"]) ? string.Empty : dataRow[CustommapColumnsData["CarpetAreaUnit"]].ToString();
                string? builtupArea = !CustommapColumnsData.ContainsKey("BuiltupArea") || string.IsNullOrEmpty(CustommapColumnsData["BuiltupArea"]) ? string.Empty : dataRow[CustommapColumnsData["BuiltupArea"]].ToString();
                string? builtupAreaUnit = !CustommapColumnsData.ContainsKey("BuiltupAreaUnit") || string.IsNullOrEmpty(CustommapColumnsData["BuiltupAreaUnit"]) ? string.Empty : dataRow[CustommapColumnsData["BuiltupAreaUnit"]].ToString();
                string? superBuiltupArea = !CustommapColumnsData.ContainsKey("SuperBuiltupArea") || string.IsNullOrEmpty(CustommapColumnsData["SuperBuiltupArea"]) ? string.Empty : dataRow[CustommapColumnsData["SuperBuiltupArea"]].ToString();
                string? superBuiltupAreaUnit = !CustommapColumnsData.ContainsKey("SuperBuiltupAreaUnit") || string.IsNullOrEmpty(CustommapColumnsData["SuperBuiltupAreaUnit"]) ? string.Empty : dataRow[CustommapColumnsData["SuperBuiltupAreaUnit"]].ToString();
                string? areaUnit = !CustommapColumnsData.ContainsKey("AreaUnit") || string.IsNullOrEmpty(CustommapColumnsData["AreaUnit"]) ? string.Empty : dataRow[CustommapColumnsData["AreaUnit"]].ToString();
                string? pricePerUnit = !CustommapColumnsData.ContainsKey("PricePerUnit") || string.IsNullOrEmpty(CustommapColumnsData["PricePerUnit"]) ? string.Empty : dataRow[CustommapColumnsData["PricePerUnit"]].ToString();
                string? totalPrice = !CustommapColumnsData.ContainsKey("TotalPrice") || string.IsNullOrEmpty(CustommapColumnsData["TotalPrice"]) ? string.Empty : dataRow[CustommapColumnsData["TotalPrice"]].ToString();
                string? unitType = !CustommapColumnsData.ContainsKey("UnitType") || string.IsNullOrEmpty(CustommapColumnsData["UnitType"]) ? string.Empty : dataRow[CustommapColumnsData["UnitType"]].ToString();
                string? unitSubType = !CustommapColumnsData.ContainsKey("UnitSubType") || string.IsNullOrEmpty(CustommapColumnsData["UnitSubType"]) ? string.Empty : dataRow[CustommapColumnsData["UnitSubType"]].ToString();
                string? currecncy = !CustommapColumnsData.ContainsKey("Currency") || string.IsNullOrEmpty(CustommapColumnsData["Currency"]) ? string.Empty : dataRow[CustommapColumnsData["Currency"]].ToString();
                string? maintenanceCost = !CustommapColumnsData.ContainsKey("MaintenanceCost") || string.IsNullOrEmpty(CustommapColumnsData["MaintenanceCost"]) ? string.Empty : dataRow[CustommapColumnsData["MaintenanceCost"]].ToString();
                string? balconies = !CustommapColumnsData.ContainsKey("Balconies") || string.IsNullOrEmpty(CustommapColumnsData["Balconies"]) ? string.Empty : dataRow[CustommapColumnsData["Balconies"]].ToString();
                string? bathRooms = !CustommapColumnsData.ContainsKey("BathRooms") || string.IsNullOrEmpty(CustommapColumnsData["BathRooms"]) ? string.Empty : dataRow[CustommapColumnsData["BathRooms"]].ToString();
                string? drawingOrLivingRooms = !CustommapColumnsData.ContainsKey("DrawingOrLivingRooms") || string.IsNullOrEmpty(CustommapColumnsData["DrawingOrLivingRooms"]) ? string.Empty : dataRow[CustommapColumnsData["DrawingOrLivingRooms"]].ToString();
                string? bedRooms = !CustommapColumnsData.ContainsKey("BedRooms") || string.IsNullOrEmpty(CustommapColumnsData["BedRooms"]) ? string.Empty : dataRow[CustommapColumnsData["BedRooms"]].ToString();
                string? utilities = !CustommapColumnsData.ContainsKey("Utilities") || string.IsNullOrEmpty(CustommapColumnsData["Utilities"]) ? string.Empty : dataRow[CustommapColumnsData["Utilities"]].ToString();
                string? kitchens = !CustommapColumnsData.ContainsKey("Kitchens") || string.IsNullOrEmpty(CustommapColumnsData["Kitchens"]) ? string.Empty : dataRow[CustommapColumnsData["Kitchens"]].ToString();
                string? maximumOccupants = !CustommapColumnsData.ContainsKey("MaximumOccupants") || string.IsNullOrEmpty(CustommapColumnsData["MaximumOccupants"]) ? string.Empty : dataRow[CustommapColumnsData["MaximumOccupants"]].ToString();

                var projectInfo = GetProjectType(unitType, unitSubType, projectTypes, bhkType, noOfBhks);
                string? currencycode;
                if (!string.IsNullOrEmpty(currecncy))
                {
                    currencycode = CustommapColumnsData.GetCurrencySymbol1(dataRow, currecncy, countries?.FirstOrDefault()?.DefaultCurrency);
                }
                else
                {
                    currencycode = project?.MonetaryInfo?.Currency;
                }
                var unitarea = GetUnitDetails(unitArea ?? string.Empty, unitTypes, areaUnit ?? string.Empty, defaultUnitId);
                var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, unitTypes, carpetAreaUnit ?? string.Empty, unitarea.Item2 );
                var builtuparea = GetUnitDetails(builtupArea ?? string.Empty, unitTypes, builtupAreaUnit ?? string.Empty, unitarea.Item2);
                var superbuiltuparea = GetUnitDetails(superBuiltupArea ?? string.Empty, unitTypes, superBuiltupAreaUnit ?? string.Empty, unitarea.Item2);

                List<UnitTypeAttribute>? newAttributes=null;
                if(balconies==string.Empty && bathRooms == string.Empty && drawingOrLivingRooms == string.Empty && bedRooms == string.Empty && utilities == string.Empty && kitchens == string.Empty && maximumOccupants == string.Empty)
                {
                    newAttributes = null;
                }
                else
                {
                    newAttributes = GetUnitTypeAttribute(new Dictionary<UnitDataColumn, string>()
                    {
                        { UnitDataColumn.Balconies, balconies ?? string.Empty},
                        { UnitDataColumn.BathRooms, bathRooms ?? string.Empty},
                        { UnitDataColumn.DrawingOrLivingRooms, drawingOrLivingRooms ?? string.Empty},
                        { UnitDataColumn.BedRooms,bedRooms ?? string.Empty},
                        { UnitDataColumn.Utilities,utilities ?? string.Empty},
                        { UnitDataColumn.Kitchens,kitchens ?? string.Empty},
                        { UnitDataColumn.MaximumOccupants,maximumOccupants ?? string.Empty}
                    }, unitTypesAttributes);
                }
                var unit = new UnitType()
                {
                    Id = Guid.NewGuid(),
                    Name = name,
                    Area = unitarea.Item1,
                    CarpetArea = carpetarea.Item1 != 0 ? carpetarea.Item1 : unitarea.Item1,
                    CarpetAreaUnitId = carpetarea.Item2,
                    BuildUpArea = builtuparea.Item1 != 0 ? builtuparea.Item1 : unitarea.Item1,
                    BuildUpAreaId = builtuparea.Item2,
                    SuperBuildUpArea = superbuiltuparea.Item1 != 0 ? superbuiltuparea.Item1 : unitarea.Item1,
                    SuperBuildUpAreaUnit = superbuiltuparea.Item2,
                    PricePerUnit = ConvertStringToDouble(pricePerUnit),
                    Price = ConvertStringToDouble(totalPrice),
                    FurnishingStatus = ConvertStringToEnum<FurnishStatus>(furnishingStatus),
                    Facings = GetFacings(facing),
                    AreaUnitId = unitarea.Item2,
                    BHKType = projectInfo.IsValidInfo ? projectInfo.BHKType : default,
                    NoOfBHK = projectInfo.IsValidInfo ? projectInfo.NoOfBHK : default,
                    MasterUnitType = projectInfo.IsValidInfo ? projectInfo.ProjectType : default,
                    Currency = currencycode ?? project?.MonetaryInfo?.Currency ?? "INR",
                    MaintenanceCost = ConvertStringToLong(maintenanceCost),
                    Attributes = newAttributes
                };
                units.Add(unit);
                var formFieldValue = formFields.Where(field => field.EntityChildId == project?.ProjectType?.Id).Select(field =>
                {
                    var fieldDisplayName = field.FieldDisplayName;
                    var hasValue = CustommapColumnsData.ContainsKey(fieldDisplayName) && !string.IsNullOrWhiteSpace(CustommapColumnsData[fieldDisplayName]);
                    var fieldsValue = hasValue ? dataRow[CustommapColumnsData[fieldDisplayName]].ToString() : string.Empty;
                    return new CustomFieldValue
                    {
                        Id = Guid.NewGuid(),
                        FormFieldId = field.Id,
                        EntityId = project?.Id,
                        Value = fieldsValue,
                        IsDeleted = false,
                        CreatedOn = DateTime.UtcNow,
                        LastModifiedOn = DateTime.UtcNow,
                        EntityChildId = unit?.Id
                    };
                }).Where(f => !string.IsNullOrWhiteSpace(f.Value));

                formFieldValues.AddRange(formFieldValue);
            }
            return (units, formFieldValues);
        }
        public static List<Facing> GetFacings(string facings)
        {
            List<Facing> facing = new();
            if (!string.IsNullOrEmpty(facings))
            {
                foreach (string item in facings.Split(','))
                {
                    string cleanedItem = RemoveSpecialCharactersAndNumbers(item);

                    if (Enum.TryParse<Facing>(cleanedItem, true, out Facing type))
                    {
                        facing.Add(type);
                    }
                }
            }
            return facing;
        }

        private static string RemoveSpecialCharactersAndNumbers(string input)
        {
            return Regex.Replace(input, @"[^a-zA-Z]", "").ToLower();
        }
        public static double ConvertStringToDouble(string? input)
        {
            double result;
            if (double.TryParse(input, out result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static long ConvertStringToLong(string? input)
        {
            long result;
            if (long.TryParse(input, out result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static TEnum ConvertStringToEnum<TEnum>(string? value) where TEnum : struct
        {
            string status = RemoveSpecialCharactersAndNumbers(value);
            if (status.Equals("fullyfurnished"))
            {
                status = "furnished";
            }
            if (Enum.TryParse(status, true, out TEnum result))
            {
                return result;
            }
            else
            {
                return default;
            }
        }
        public static Guid? GetUnitId(string? input, List<MasterAreaUnit> unitTypes)
        {
            if (string.IsNullOrEmpty(input))
            {
                return null;
            }
            else
            {
                if (unitTypes?.Any(i => i.Unit.Trim().ToLower().Contains(input?.ToLower()?.Trim() ?? string.Empty)) ?? false)
                {
                    return unitTypes.FirstOrDefault(i => i.Unit.Trim().ToLower().Contains(input?.ToLower()?.Trim() ?? string.Empty))?.Id ?? Guid.Empty;
                }
            }
            return Guid.Empty;
        }

        public static List<string> GetUnmappedUnitColumnNames(this DataTable table, Dictionary<UnitDataColumn, string> mappedColumnsData)
        {
            List<string> columns = new();
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static List<string> GetUnmappedUnitColumnNamesV1(this DataTable table, Dictionary<string, string> mappedColumnsData)
        {
            List<string> columns = new();
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static void SetUnit(this Domain.Entities.UnitType unit, Guid userId)
        {
            if (unit != null)
            {
                unit.CreatedBy = userId;
                unit.LastModifiedBy = userId;
            }

        }
        public static MemoryStream CreateExcelData(List<InvalidUnitDto> inValidData)
        {
            using MemoryStream stream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;

            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidData.FirstOrDefault();
            Type objType = obj.GetType();

            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }

            sheetData.Append(row1);
            foreach (var inValid in inValidData)
            {
                Row row = new Row();
                CreateCell(row, inValid.Name);
                CreateCell(row, inValid.Area);
                CreateCell(row, inValid.Notes);
                CreateCell(row, inValid.Errors);
                CreateCell(row, inValid.RepeatedCount.ToString(), CellValues.Number);
                sheetData.Append(row);

            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static void CreateCell(Row row, string value, CellValues type = CellValues.String)
        {
            Cell cell = new Cell()
            {
                CellValue = new CellValue(value),
                DataType = type,
            };
            row.Append(cell);
        }
        public static ProjectTypeInfo GetProjectType(string baseProjectType, string subProjectType, List<MasterProjectType> projectTypes, string bhkType, string? noOfBHK)
        {
            MasterProjectType projectType = null;
            List<string> projecTypes = projectTypes?.Select(i => i.DisplayName.ToLower()).ToList();
            BHKType bHKType = default;
            double bHK = GetNoOfBHK(noOfBHK);
            if (!string.IsNullOrEmpty(bhkType) || !string.IsNullOrEmpty(noOfBHK))
            {
                if ((Enum.TryParse<BHKType>(bhkType, true, out bHKType) || bHK != 0) && (string.IsNullOrEmpty(baseProjectType) && string.IsNullOrEmpty(subProjectType)))
                {
                    projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.Id == Guid.Parse("8ba96762-16f2-4735-bc5a-138573081a19"));
                    return new ProjectTypeInfo()
                    {
                        ProjectType = projectType,
                        BHKType = bHKType,
                        NoOfBHK = bHK,
                        IsValidInfo = true
                    };
                }
            }
            else
            {
                if (string.IsNullOrEmpty(baseProjectType) && string.IsNullOrEmpty(subProjectType))
                {
                    return new ProjectTypeInfo() { IsValidInfo = false };
                }
            }
            string subProject = string.Empty;
            string baseProject = string.Empty;
            if (!string.IsNullOrEmpty(subProjectType))
            {
                subProject = subProjectType;
            }
            else
            {
                if (!string.IsNullOrEmpty(baseProjectType))
                {
                    if (baseProjectType.ToLower().Contains("residential"))
                    {
                        subProject = "flat";
                    }
                    if (baseProjectType.ToLower().Contains("commercial"))
                    {
                        subProject = "Plot";
                    }
                    if (baseProjectType.ToLower().Contains("agricultural"))
                    {
                        subProject = "land";
                    }
                }
            }

            if (!string.IsNullOrEmpty(baseProjectType) && baseProjectType?.ToLower() == "commercial")
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower().Trim() == subProject.ToLower().Trim() && !i.IsDeleted);   
            }
            if (string.IsNullOrEmpty(baseProjectType) || baseProjectType?.ToLower() == "residential")
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower().Trim() == subProject.ToLower().Trim() && !i.IsDeleted);
            }
            else
            {
                projectType = projectTypes.FirstOrDefault(i => i.DisplayName.ToLower() == subProject.ToLower());
            }
            return new ProjectTypeInfo()
            {
                ProjectType = projectType,
                BHKType = bHKType,
                NoOfBHK = bHK,
                IsValidInfo = true
            };
        }
        public static string GetCurrencySymbol1(this Dictionary<string, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
                .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                    .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                    .SelectMany(c => c.Currencies)
                    .Select(currency => currency.IsoCode))
                .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }

        }
        public static string GetCurrencySymbol1(this Dictionary<UnitDataColumn, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
           .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
           .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
           .SelectMany(c => c.Currencies)
           .Select(currency => currency.IsoCode))
           .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }

        }

        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }
        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                } 
            }
            return (unitArea, Guid.Empty);
        }
        public static double GetNoOfBHK(string noOfBHK)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?");
                Match match = regex.Match(noOfBHK);
                double integer = 0;

                if (match.Success)
                {
                    integer = double.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static List<UnitTypeAttribute> GetUnitTypeAttribute(Dictionary<UnitDataColumn, string> attributes, List<CustomMasterAttribute> masterProjectUnitAttributes)
        {
            List<UnitTypeAttribute> unitTypeAttributes = new List<UnitTypeAttribute>();
            foreach (var attribute in attributes)
            {
                var attributeValue = GetNumber(attribute.Value);
                UnitTypeAttribute unitTypeAttribute = new();
                if (masterProjectUnitAttributes.Count > 0)
                {
                    var formattedKey = SplitCamelCase(attribute.Key.ToString()).ToLower();
                    var matchingAttribute = masterProjectUnitAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey));
                    if (matchingAttribute != null)
                    {
                        unitTypeAttribute.MasterProjectUnitAttributeId = masterProjectUnitAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey))?.Id ?? Guid.Empty;
                        unitTypeAttribute.Value = attributeValue.ToString();
                        unitTypeAttributes.Add(unitTypeAttribute);
                    }
                }
            }
            return unitTypeAttributes;
        }
        public static double GetNumber(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(number);
                double integer = 0;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        private static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }

        public static List<InvalidUnitDto> GetInvalidUnits(List<Domain.Entities.UnitType> units)
        {
            List<InvalidUnitDto> invalidData = new();
        
            List<Domain.Entities.UnitType>? invalidunits = null;
            invalidunits = units.Where(i => string.IsNullOrEmpty(i.Name)).ToList();
            if (invalidunits.Any())
            {
                var invalidName = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidName.ForEach(i => i.Errors = "Invalid Name");
                units.RemoveAll(i => string.IsNullOrEmpty(i.Name));
                invalidData.AddRange(invalidName);
                invalidunits = null;
            }


            invalidunits = units.Where(i => i.Area == default).ToList();
            if (invalidunits.Any())
            {
                var invalidArea = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidArea.ForEach(i => i.Errors = "Invalid Area");
                units.RemoveAll(i => i.Area == default);
                invalidData.AddRange(invalidArea);
                invalidunits = null;
            }

            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.BHKType!=0).ToList();
            if (invalidunits.Any())
            {
                var invalidBhkType = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidBhkType.ForEach(i => i.Errors = "Invalid BhkType");
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id==Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.BHKType != 0);
                invalidData.AddRange(invalidBhkType);
                invalidunits = null;
            }


            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.NoOfBHK != 0).ToList();
            if (invalidunits.Any())
            {
                var invalidbhk = invalidunits.Adapt<List<InvalidUnitDto>>();
                invalidbhk.ForEach(i => i.Errors = "Invalid NoofBhk");
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.NoOfBHK != 0);
                invalidData.AddRange(invalidbhk);
                invalidunits = null;
            }

            invalidunits = units.Where(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.Attributes!=null ).ToList();
            if (invalidunits.Any())
            {
                var invalidAttributes = new List<InvalidUnitDto>();
                foreach (var unit in invalidunits)
                {
                    string attributes = string.Empty;
                    if (unit.Attributes != null)
                    {
                        attributes = string.Join(", ", unit.Attributes.Select(a => a.Value));
                    }
                    var invalidUnitDto = unit.Adapt<InvalidUnitDto>();
                    invalidUnitDto.Attributes = attributes;
                    invalidUnitDto.Errors = "Invalid Attributes";
                    invalidAttributes.Add(invalidUnitDto);
                }
                units.RemoveAll(i => (i.MasterUnitType?.BaseId != Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.MasterUnitType?.Id == Guid.Parse("dbafbc86-523d-4bb0-b403-a5c8e05f5bb9")) && i.Attributes != null);
                invalidData.AddRange(invalidAttributes);
                invalidunits = null;
            }
            return invalidData;
        }
    }
}
