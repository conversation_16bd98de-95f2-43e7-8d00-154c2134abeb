﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Project.Web
{
    public class CreateProjectRequest : CreateProjectDto, IRequest<Response<Guid>>
    {
    }
    public class CreateProjectRequestHandler : ProjectCommonRequestHandler, IRequestHandler<CreateProjectRequest, Response<Guid>>
    {
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterProjectType> _masterProjectTypeRepo;
        private readonly IRepositoryWithEvents<Block> _projectBlockRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        private readonly IRepositoryWithEvents<AssociatedBank> _associatesBankRepo;
        private readonly IRepositoryWithEvents<MasterAssociatedBank> _masterAssociatesBankRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;


        public CreateProjectRequestHandler(
            IRepositoryWithEvents<Block> blockRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterProjectType> masterProjectTypeRepo,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<CustomMasterProjectType> customProjectTypeRepo,
            IRepositoryWithEvents<AssociatedBank> associatesBankRepo,
            IRepositoryWithEvents<MasterAssociatedBank> masterAssociatesBankRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo
            ) : base(serviceProvider)
        {
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterProjectTypeRepo = masterProjectTypeRepo;
            _projectBlockRepo = blockRepo;
            _customProjectTypeRepo = customProjectTypeRepo;
            _masterAssociatesBankRepo = masterAssociatesBankRepo;
            _associatesBankRepo = associatesBankRepo;
            _globalsettingRepo = globalsettingRepo;
        }

        public async Task<Response<Guid>> Handle(CreateProjectRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);

            MasterProjectType? masterProjectType = null;
            if (request.ProjectTypeId != Guid.Empty && request.ProjectTypeId != default)
            {
                masterProjectType = await _masterProjectTypeRepo.GetByIdAsync(request.ProjectTypeId ?? Guid.Empty, cancellationToken);
                if (masterProjectType == null)
                {
                    throw new InvalidOperationException("Project type id does not belong to Master data.");
                }
            }

            #region Address

            var address = await CreateAddressAsync(request.Address, cancellationToken);

            #endregion

            Domain.Entities.Project project = request.Adapt<Domain.Entities.Project>();
            project.Name = project.Name.Trim();
            ProjectBuilderDetails builderDetails = request.BuilderDetail?.Adapt<ProjectBuilderDetails>() ?? new();
            project.Address = address;
            project.BuilderDetail = builderDetails;
            project.ReraNumber = request.ReraNumbers;
            project.ProjectType = masterProjectType;
            project.MonetaryInfo ??= new();
            project.MonetaryInfo.Currency = request?.MonetaryInfo?.Currency ?? countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";

            try
            {
                project = await _projectRepo.AddAsync(project, cancellationToken);

                #region Associates Bank
                if (request?.AssociateBank?.Any() ?? false)
                {
                    foreach (var bankId in request.AssociateBank)
                    {
                        var fetchedPropertyAmenity = await _masterAssociatesBankRepo.GetByIdAsync(bankId);
                        if (fetchedPropertyAmenity == null)
                        {
                            throw new Exception($"{bankId} Id does not belong to master project Amenity data.");
                        }
                    }
                    foreach (var amenityId in request.AssociateBank)
                    {
                        var projectBank = new AssociatedBank();
                        projectBank.MasterAssociatedBankId = amenityId;
                        projectBank.ProjectId = project.Id;
                        await _associatesBankRepo.AddAsync(projectBank);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateProjectRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            return new Response<Guid>(project.Id);

        }
    }
}
