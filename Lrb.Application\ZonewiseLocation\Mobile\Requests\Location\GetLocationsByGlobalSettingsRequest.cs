﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.ZonewiseLocation.Mobile.Dtos;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Newtonsoft.Json;

namespace Lrb.Application.ZonewiseLocation.Mobile.Requests
{
    public class GetLocationsByGlobalSettingsRequest : PaginationFilter, IRequest<PagedResponse<LocationLeadDto, string>>
    {
        public string? SearchText { get; set; }
    }
    public class GetLocationsBySettingsRequestHandler : IRequestHandler<GetLocationsByGlobalSettingsRequest, PagedResponse<LocationLeadDto, string>>
    {
        private readonly IReadRepository<Location> _locationRepo;
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        protected readonly IGooglePlacesService _googlePlacesService;

        public GetLocationsBySettingsRequestHandler(IReadRepository<Location> locationRepo,
            IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IGooglePlacesService googlePlacesService)
        {
            _locationRepo = locationRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _googlePlacesService = googlePlacesService;
        }
        public async Task<PagedResponse<LocationLeadDto, string>> Handle(GetLocationsByGlobalSettingsRequest request, CancellationToken cancellationToken)
        {
            var storedLocationDtos = new List<LocationLeadDto>();
            List<AutocompleteLocationModel> locationModels = new();

            var globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var countries = JsonConvert.DeserializeObject<List<Lrb.Application.CuntryInformation.Web.Dto.CountryInfoDto>>(globalSettings?.CountriesInfo);
            string? countryName = null;
            if (globalSettings != null && countries != null)
            {
                foreach (var country in countries)
                {
                   countryName = country.Name;
                }
            }

            if (!globalSettings?.IsZoneLocationEnabled ?? false)
            {
                locationModels = (await _googlePlacesService.GetPlacesAutocompleteResultAsync(request.SearchText ?? string.Empty, countryName));
                storedLocationDtos.AddRange(locationModels.Adapt<List<LocationLeadDto>>());
                return new(storedLocationDtos.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize), storedLocationDtos.Count);
            }
            else
            {
                var locations = await _locationRepo.ListAsync(new LocationBySearchSpec(request), cancellationToken);
                storedLocationDtos.AddRange(locations.Adapt<List<LocationLeadDto>>());
                var storedLocationsCount = await _locationRepo.CountAsync(new LocationCountSpec(request), cancellationToken);
                if (globalSettings?.IsGoogleMapLocationEnabled ?? false)
                {
                    locationModels = (await _googlePlacesService.GetPlacesAutocompleteResultAsync(request.SearchText ?? string.Empty, countryName));
                }
                if (locationModels.Any())
                {
                    storedLocationDtos.AddRange(locationModels.Adapt<List<LocationLeadDto>>());
                    storedLocationsCount += locationModels.Count;
                }
                return new(storedLocationDtos.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize), storedLocationsCount);
            }
        }
    }
}
