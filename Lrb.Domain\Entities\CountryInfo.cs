﻿using System.Text.Json.Serialization;

namespace Lrb.Domain.Entities
{
    public class CountryInfo : AuditableEntity, IAggregateRoot
    {
        public string? Name { get; set; }
        public List<string>? CallingCode { get; set; }
        public string? DefaultCallingCode { get; set; }
        public List<string>? Codes { get; set; }
        public string? Code { get; set; }
        public string? DefaultCurrency { get; set; }
        public string? Icon { get; set; }
        public string? TimeZoneId { get; set; }
        public string? DefaultSymbol { get; set; }
        public Guid? GlobalSettingsId { get; set; }
        public IList<Currencies>? Currencies { get; set; }
    }

    public class Currencies : AuditableEntity, IAggregateRoot
    {
        public string? Symbol { get; set; }
        public string? Currency { get; set; }
        public Guid? CountryInfoId { get; set; }

    }
}





