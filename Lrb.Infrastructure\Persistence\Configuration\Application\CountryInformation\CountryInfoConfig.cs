﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.CountryInformation
{ 
    public class CountryInfoConfig : IEntityTypeConfiguration<CountryInfo>
    {
        public void Configure(EntityTypeBuilder<CountryInfo> builder)
        {
            builder.IsMultiTenant();
        
            builder.HasMany(g => g.Currencies);


        }
    }
}
