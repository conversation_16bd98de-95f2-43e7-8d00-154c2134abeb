using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class GlobalSettingsConfig : IEntityTypeConfiguration<GlobalSettings>
    {
        public void Configure(EntityTypeBuilder<GlobalSettings> builder)
        {
            builder.IsMultiTenant();
            // Removed Countries relationship configuration
            builder.Property(i => i.DefaultValues).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ShouldEnableEnquiryForm).HasDefaultValue(true);

        }
    }
    
}
