using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class RemoveCountryInfoAndCurrenciesTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, migrate existing CountryInfo data to JSON format in GlobalSettings.CountriesInfo
            migrationBuilder.Sql(@"
                UPDATE ""LeadratBlack"".""GlobalSettings"" 
                SET ""CountriesInfo"" = COALESCE(
                    (
                        SELECT json_agg(
                            json_build_object(
                                'Name', ci.""Name"",
                                'CallingCode', ci.""CallingCode"",
                                'DefaultCallingCode', ci.""DefaultCallingCode"",
                                'Codes', ci.""Codes"",
                                'Code', ci.""Code"",
                                'DefaultCurrency', ci.""DefaultCurrency"",
                                'Icon', ci.""Icon"",
                                'TimeZoneId', ci.""TimeZoneId"",
                                'DefaultSymbol', ci.""DefaultSymbol"",
                                'Currencies', (
                                    SELECT json_agg(
                                        json_build_object(
                                            'Symbol', c.""Symbol"",
                                            'Currency', c.""Currency""
                                        )
                                    )
                                    FROM ""LeadratBlack"".""Currencies"" c 
                                    WHERE c.""CountryInfoId"" = ci.""Id""
                                )
                            )
                        )
                        FROM ""LeadratBlack"".""CountryInfo"" ci 
                        WHERE ci.""GlobalSettingsId"" = ""GlobalSettings"".""Id""
                    )::text,
                    '[{""Name"":null,""CallingCode"":[],""DefaultCallingCode"":""+91"",""Codes"":[],""Code"":""IN"",""DefaultCurrency"":""INR"",""Icon"":null,""TimeZoneId"":null,""DefaultSymbol"":""₹"",""Currencies"":[{""Symbol"":""₹"",""Currency"":""INR""}]}]'
                )
                WHERE ""CountriesInfo"" IS NULL OR ""CountriesInfo"" = '';
            ");

            // Drop foreign key constraints first
            migrationBuilder.DropForeignKey(
                name: "FK_Currencies_CountryInfo_CountryInfoId",
                schema: "LeadratBlack",
                table: "Currencies");

            migrationBuilder.DropForeignKey(
                name: "FK_CountryInfo_GlobalSettings_GlobalSettingsId",
                schema: "LeadratBlack",
                table: "CountryInfo");

            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_Currencies_CountryInfoId",
                schema: "LeadratBlack",
                table: "Currencies");

            migrationBuilder.DropIndex(
                name: "IX_CountryInfo_GlobalSettingsId",
                schema: "LeadratBlack",
                table: "CountryInfo");

            // Drop tables in correct order (child tables first)
            migrationBuilder.DropTable(
                name: "Currencies",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "CountryInfo",
                schema: "LeadratBlack");

            // Remove the IsTimeZoneEnabled column from GlobalSettings
            migrationBuilder.DropColumn(
                name: "IsTimeZoneEnabled",
                schema: "LeadratBlack",
                table: "GlobalSettings");

            // Set default JSON value for CountriesInfo where it's null and add default constraint
            migrationBuilder.Sql(@"
                UPDATE ""LeadratBlack"".""GlobalSettings""
                SET ""CountriesInfo"" = '[{""Name"":null,""CallingCode"":[],""DefaultCallingCode"":""+91"",""Codes"":[],""Code"":""IN"",""DefaultCurrency"":""INR"",""Icon"":null,""TimeZoneId"":null,""DefaultSymbol"":""₹"",""Currencies"":[{""Symbol"":""₹"",""Currency"":""INR""}]}]'
                WHERE ""CountriesInfo"" IS NULL OR ""CountriesInfo"" = '';
            ");

            // Add default constraint for new records
            migrationBuilder.Sql(@"
                ALTER TABLE ""LeadratBlack"".""GlobalSettings""
                ALTER COLUMN ""CountriesInfo""
                SET DEFAULT '[{""Name"":null,""CallingCode"":[],""DefaultCallingCode"":""+91"",""Codes"":[],""Code"":""IN"",""DefaultCurrency"":""INR"",""Icon"":null,""TimeZoneId"":null,""DefaultSymbol"":""₹"",""Currencies"":[{""Symbol"":""₹"",""Currency"":""INR""}]}]';
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Add back the IsTimeZoneEnabled column
            migrationBuilder.AddColumn<bool>(
                name: "IsTimeZoneEnabled",
                schema: "LeadratBlack",
                table: "GlobalSettings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            // Recreate CountryInfo table
            migrationBuilder.CreateTable(
                name: "CountryInfo",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    CallingCode = table.Column<List<string>>(type: "text[]", nullable: true),
                    DefaultCallingCode = table.Column<string>(type: "text", nullable: true),
                    Codes = table.Column<List<string>>(type: "text[]", nullable: true),
                    Code = table.Column<string>(type: "text", nullable: true),
                    DefaultCurrency = table.Column<string>(type: "text", nullable: true),
                    Icon = table.Column<string>(type: "text", nullable: true),
                    TimeZoneId = table.Column<string>(type: "text", nullable: true),
                    DefaultSymbol = table.Column<string>(type: "text", nullable: true),
                    GlobalSettingsId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CountryInfo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CountryInfo_GlobalSettings_GlobalSettingsId",
                        column: x => x.GlobalSettingsId,
                        principalSchema: "LeadratBlack",
                        principalTable: "GlobalSettings",
                        principalColumn: "Id");
                });

            // Recreate Currencies table
            migrationBuilder.CreateTable(
                name: "Currencies",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Symbol = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    CountryInfoId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Currencies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Currencies_CountryInfo_CountryInfoId",
                        column: x => x.CountryInfoId,
                        principalSchema: "LeadratBlack",
                        principalTable: "CountryInfo",
                        principalColumn: "Id");
                });

            // Recreate indexes
            migrationBuilder.CreateIndex(
                name: "IX_CountryInfo_GlobalSettingsId",
                schema: "LeadratBlack",
                table: "CountryInfo",
                column: "GlobalSettingsId");

            migrationBuilder.CreateIndex(
                name: "IX_Currencies_CountryInfoId",
                schema: "LeadratBlack",
                table: "Currencies",
                column: "CountryInfoId");
        }
    }
}
