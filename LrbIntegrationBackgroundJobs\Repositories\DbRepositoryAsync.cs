﻿using Dapper;
using LrbIntegrationBackgroundJobs.Dtos.PFV2;
using Microsoft.Extensions.Options;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LrbIntegrationBackgroundJobs
{
    public class DbRepositoryAsync : IDbRepositoryAsync
    {
        private readonly DatabaseSettings _settings;
        public DbRepositoryAsync(IOptions<DatabaseSettings> dbOptions)
        {
            _settings = dbOptions.Value;
        }
        public async Task<List<TenantInfo>?> GetTenantWithConnectionStringAsync()
        {
            await using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                var combinedQuery = $"SELECT \"Id\", \"ConnectionString\" FROM \"MultiTenancy\".\"Tenants\" WHERE \"ConnectionString\" != '';";
                await connection.OpenAsync();
                var result = await connection.QueryAsync<TenantInfo>(combinedQuery);
                await connection.CloseAsync();
                return result?.ToList() ?? default;
            }
            catch (Exception ex)
            {
                return null;
            }
            finally { await connection.CloseAsync(); }
        }

        #region JustLead
        public async Task<List<IntegrationAccountDto>> GetAllJustLeadIntegrationsAsync(string connectionString)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 20 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                throw;
            }
            finally { await connection.CloseAsync(); }
        }
        public async Task<List<IntegrationAccountDto>> GetAllJustLeadIntegrationsAsync()
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 20 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                var mainAccounts = result.ToList();
                try
                {
                    var externalTenants = await GetTenantWithConnectionStringAsync();
                    if (externalTenants?.Any() ?? false)
                    {
                        foreach (var account in externalTenants)
                        {
                            var accounts = await GetAllJustLeadIntegrationsAsync(account.ConnectionString);
                            if (accounts?.Any() ?? false)
                            {
                                accounts.ForEach(i => i.ConnectionString = account.ConnectionString);
                                mainAccounts.RemoveAll(i => i.TenantId == account.Id);
                                mainAccounts.AddRange(accounts);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //ignore
                }
                return mainAccounts;
            }
            catch
            {
                throw;
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion

        #region CommonFloor
        public async Task<List<IntegrationAccountDto>> GetAllCommonFloorIntegrationsAsync(string connectionString)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 30 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                throw;
            }
            finally { await connection.CloseAsync(); }
        }
        public async Task<List<IntegrationAccountDto>> GetAllCommonFloorIntegrationsAsync()
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 30 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                var mainAccounts = result.ToList();
                try
                {
                    var externalTenants = await GetTenantWithConnectionStringAsync();
                    if (externalTenants?.Any() ?? false)
                    {
                        foreach (var account in externalTenants)
                        {
                            var accounts = await GetAllCommonFloorIntegrationsAsync(account.ConnectionString);
                            if (accounts?.Any() ?? false)
                            {
                                accounts.ForEach(i => i.ConnectionString = account.ConnectionString);
                                mainAccounts.RemoveAll(i => i.TenantId == account.Id);
                                mainAccounts.AddRange(accounts);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //ignore
                }
                return mainAccounts;
            }
            catch
            {
                throw;
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion

        public async Task<bool> UpdateSyncedCountAsync(IntegrationAccountDto entity)
        {
            var query = $"UPDATE \"LeadratBlack\".\"IntegrationAccountInfo\" SET \"SyncedCount\" = @count WHERE \"Id\" = @id";
            var filters = new Dictionary<string, object>
            {
                { "@id", entity.Id },
                { "@count", entity.SyncedCount }
            };
            await using var connection = new NpgsqlConnection(string.IsNullOrEmpty(entity.ConnectionString) ? _settings.ConnectionString : entity.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = connection.Execute(query, filters);
                await connection.CloseAsync();
                return true;
            }
            catch
            {
                throw;
            }
            finally { await connection.CloseAsync(); }
        }


        #region Bayut
        public async Task<List<IntegrationAccountDto>> GetAllBayutIntegrationsAsync(string connectionString)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 39 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }
        public async Task<List<IntegrationAccountDto>> GetAllBayutIntegrationsAsync()
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 39 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                var mainAccounts = result.ToList();
                try
                {
                    var externalTenants = await GetTenantWithConnectionStringAsync();
                    if (externalTenants?.Any() ?? false)
                    {
                        var data = externalTenants.GroupBy(i => i.ConnectionString).Select(i => i.Key);
                        foreach (var connectionString in data)
                        {
                            var accounts = await GetAllBayutIntegrationsAsync(connectionString);
                            if (accounts?.Any() ?? false)
                            {
                                accounts.ForEach(i => i.ConnectionString = connectionString);
                                mainAccounts.AddRange(accounts);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //ignore
                }
                return mainAccounts;
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion

        #region PropertFinder
        public async Task<List<IntegrationAccountDto>> GetAllPropertFinderIntegrationsAsync(string connectionString)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 38 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }

        public async Task<List<IntegrationAccountDto>> GetAllPropertFinderIntegrationsAsync()
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 38 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                var mainAccounts = result.ToList();
                try
                {
                    var externalTenants = await GetTenantWithConnectionStringAsync();
                    if (externalTenants?.Any() ?? false)
                    {
                        var data = externalTenants.GroupBy(i => i.ConnectionString).Select(i => i.Key);
                        foreach (var connectionString in data)
                        {
                            var accounts = await GetAllPropertFinderIntegrationsAsync(connectionString);
                            if (accounts?.Any() ?? false)
                            {
                                accounts.ForEach(i => i.ConnectionString = connectionString);
                                mainAccounts.AddRange(accounts);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //ignore
                }
                return mainAccounts;
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }

        public async Task<string> GetDefaultCallingCodeAsync(string tenantId)
        {
            // Updated to extract DefaultCallingCode from CountriesInfo JSON column
            var query = $@"
                SELECT COALESCE(
                    (SELECT json_extract_path_text(json_array_elements(""CountriesInfo""::json), 'DefaultCallingCode')
                     FROM ""LeadratBlack"".""GlobalSettings""
                     WHERE ""TenantId"" = '{tenantId}'
                     LIMIT 1),
                    '+91'
                ) as ""DefaultCallingCode""";
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = (await connection.QueryAsync<string>(query)).FirstOrDefault();
                await connection.CloseAsync();
                return result ?? "+91";
            }
            catch
            {
                return "+91";
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion

        #region Dubizzle

        public async Task<List<IntegrationAccountDto>> GetAllDubizzleIntegrationsAsync(string connectionString)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 40 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }

        public async Task<List<IntegrationAccountDto>> GetAllDubizzleIntegrationsAsync()
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"IntegrationAccountInfo\" WHERE \"LeadSource\" = @source and \"IsDeleted\" = @isDeleted and \"Credentials\" is not null";
            var filters = new Dictionary<string, object>
            {
                { "@source", 40 },
                { "@isDeleted", false }
            };
            using var connection = new NpgsqlConnection(_settings.ConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<IntegrationAccountDto>(query, filters);
                await connection.CloseAsync();
                var mainAccounts = result.ToList();
                try
                {
                    var externalTenants = await GetTenantWithConnectionStringAsync();
                    if (externalTenants?.Any() ?? false)
                    {
                        foreach (var account in externalTenants)
                        {
                            var accounts = await GetAllDubizzleIntegrationsAsync(account.ConnectionString);
                            if (accounts?.Any() ?? false)
                            {
                                accounts.ForEach(i => i.ConnectionString = account.ConnectionString);
                                mainAccounts.RemoveAll(i => i.TenantId == account.Id);
                                mainAccounts.AddRange(accounts);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    //ignore
                }
                return mainAccounts;
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }
        public async Task<List<string>> IsLeadExistsAsync(List<string> client_phone, string tenantId, string connectionString)
        {
            var query = @"SELECT ""ContactNo"", ""AlternateContactNo"" FROM ""LeadratBlack"".""Leads"" WHERE (""ContactNo"" = Any(@contactNumber) OR ""AlternateContactNo"" = Any(@AltcontactNumber)) AND ""IsArchived"" = @isArchived AND ""IsDeleted"" = @isDeleted AND ""TenantId"" = @tenantId";
            var filters = new Dictionary<string, object>
            {
                { "@contactNumber", client_phone },
                { "@AltcontactNumber", client_phone },
                { "@isArchived", false },
                { "@isDeleted", false },
                { "@tenantId", tenantId },
            };
            using var connection = new NpgsqlConnection(string.IsNullOrEmpty(connectionString) ? _settings.ConnectionString : connectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<(string ContactNo, string AlternateContactNo)>(query, filters);
                var existingContactNumbers = result.SelectMany(r => new[] { r.ContactNo, r.AlternateContactNo }).Where(p => !string.IsNullOrWhiteSpace(p)).Distinct().ToList();
                await connection.CloseAsync();
                return existingContactNumbers;
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion

        #region Get All Lrb User
        public async Task<List<LrbUserWithThirdPartyId>> GetAllLrbUserAsync(string tenantId)
        {
            var query = $"SELECT CONCAT(\"FirstName\", ' ', \"LastName\") AS \"Name\", \"ThirdPartyId\" FROM \"Identity\".\"Users\" WHERE \"IsDeleted\" = false AND \"IsActive\" = true AND \"TenantId\" = '{tenantId}' AND \"ThirdPartyId\" Is Not Null";
       
            using var connection = new NpgsqlConnection(string.IsNullOrEmpty(_settings.ReadReplicaConnectionString) ? _settings.ConnectionString : _settings.ReadReplicaConnectionString);
            try
            {
                await connection.OpenAsync();
                var result = await connection.QueryAsync<LrbUserWithThirdPartyId>(query);
                await connection.CloseAsync();
                return result.ToList();
            }
            catch
            {
                return new();
            }
            finally { await connection.CloseAsync(); }
        }
        #endregion
    }
}
